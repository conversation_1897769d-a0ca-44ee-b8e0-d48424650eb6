{"name": "tmp", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "npx nest start", "start:local": "sh -c '. .env && nest start --watch' --host 0.0.0.0", "start:dev": "nest start --watch --host 0.0.0.0", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.14", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.14", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.14", "@nestjs/serve-static": "^5.0.3", "@nestjs/typeorm": "^11.0.0", "@types/passport-google-oauth20": "^2.0.16", "argon2": "^0.41.1", "axios": "^1.7.9", "bottleneck": "^2.19.5", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.5", "cookie-parser": "^1.4.7", "epub": "^1.3.0", "epub-parser": "^0.2.5", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "music-metadata": "^11.2.1", "nestjs-form-data": "^1.9.91", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "password-generator": "^2.3.2", "pg": "^8.13.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sharp": "^0.33.5", "slugify": "^1.6.6", "stripe": "^18.4.0", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.4", "@nestjs/testing": "^11.0.14", "@types/cookie-parser": "^1.4.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}