import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ClientModule } from "./api/client/client.module";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserModule } from './api/user/user.module';
import { AdminModule } from "./api/admin/admin.module";
import { FileSystemStoredFile, NestjsFormDataModule } from "nestjs-form-data";
import { ServeStaticModule } from '@nestjs/serve-static';
import { join} from "path";
import { FileModule } from './api/file/file.module';
import { Response } from "express";
import { FirebaseModule } from './api/firebase/firebase.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.POSTGRES_HOST,
      port: +process.env.POSTGRES_PORT,
      username: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      database: process.env.POSTGRES_DATABASE,
      autoLoadEntities: true,
      synchronize: true
    }),
    NestjsFormDataModule.config({
      isGlobal: true,
      storage: FileSystemStoredFile,
      fileSystemStoragePath: 'upload',
      cleanupAfterSuccessHandle: false,
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'upload'),
      serveRoot: '/upload',
      serveStaticOptions: {
        setHeaders: (res: Response) => {
          res.setHeader('Access-Control-Allow-Origin', '*');
        },
        fallthrough: false
      },
    }),
    UserModule,
    ClientModule,
    AdminModule,
    FileModule,
    FirebaseModule
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
