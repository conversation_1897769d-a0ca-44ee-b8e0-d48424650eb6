import {
  <PERSON>E<PERSON>ty,
  Column,
  CreateDate<PERSON>olumn,
  Entity, JoinTable, ManyToMany,
  ManyToOne, OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { IsNotEmpty } from 'class-validator';
import { AudioFileType } from '@/entity/AudioFileType';
import { AudioFileSinger } from '@/entity/AudioFileSinger';
import { AudioFileTag } from '@/entity/AudioFileTag';
import { AudioLike } from '@/entity/AudioLike';
import { AudioFileLike } from '@/entity/AudioFileLike';

@Entity()
export class AudioFile extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  external_id: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  @IsNotEmpty()
  title: string;

  @Column()
  @IsNotEmpty()
  url: string;

  @ManyToOne(type => AudioFileType, {nullable: true, onDelete: 'SET NULL'})
  type: AudioFileType;

  @ManyToOne(singer => AudioFileSinger, {nullable: true, onDelete: 'SET NULL'})
  singer: AudioFileSinger;

  @ManyToMany(tag => AudioFileTag, {nullable: true})
  @JoinTable()
  tags: AudioFileTag[];

  @Column({nullable: true})
  duration: number;

  @Column({nullable: true})
  description: string;

  @Column({nullable: true})
  comment: string;

  @OneToMany(() => AudioFileLike, like => like.audio, {cascade: true, onDelete: 'CASCADE'})
  likes: AudioFileLike[];
}