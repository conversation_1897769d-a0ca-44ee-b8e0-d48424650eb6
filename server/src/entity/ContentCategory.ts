import {En<PERSON>ty, PrimaryGeneratedColumn, Column, BaseEntity, OneToMany, OneToOne, JoinColumn} from 'typeorm';
import { Content } from "./Content";
import {File} from "@/entity/File";

@Entity('content_category')
export class ContentCategory extends BaseEntity {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({default: true})
    active: boolean;

    @OneToOne(() => File, { onDelete: 'SET NULL' })
    @JoinColumn()
    preview: File | null;

    @Column({ type: 'varchar', unique: true })
    slug!: string;

    @Column({ type: 'varchar' })
    title!: string;

    @OneToMany(() => Content, (content) => content.category)
    contents?: Content[];

    @Column({default: 1, select: true})
    order: number;
}