import {Body, Controller, Get, Param, Patch, Post, Query} from '@nestjs/common';
import { FirebaseService } from './firebase.service';
import {AudioService} from "@/api/admin/audio/audio.service";

@Controller('firebase')
export class FirebaseController {
  constructor(
      private readonly firebaseService: FirebaseService,
      private readonly audioService: AudioService
  ) {}

  @Patch('books/:key')
  async update(@Param('key') key: string, @Body() body: any) {
    return this.firebaseService.create(body)
  }

  @Post('books/remove')
  async removeBook(@Body('key') key: string) {
    return await this.firebaseService.removeBook(key)
  }

  @Post('books/checkExists')
  async checkExists(@Body('title') title: string) {
    return await this.firebaseService.checkExists(title)
  }

  @Post('lectures/remove')
  async removeLecture(@Body('key') key: string) {
    return await this.firebaseService.removeLecture(key)
  }

  @Post('lectures')
  async updateLectures(@Body() body: any) {
    return await this.audioService.importOne(body)
  }

  @Post('tags')
  async createTag(@Body() body: any) {
    return await this.firebaseService.createTag(body)
  }

  @Patch('tags')
  async updateTag(@Body() body: any) {
    return await this.firebaseService.updateTag(body)
  }

  @Get('audio/duration')
  async getAudioDuration(@Query('path') path: string) {
    return await this.firebaseService.getAudioDuration(path);
  }
}
