import { BadRequestException, Injectable } from '@nestjs/common';
import { CreatePaymentDto, PaymentProviderType } from './create-payment.dto';
import { YookassaService } from './payment-providers/yookassa.service';
import { StripeService } from './payment-providers/stripe.service';

interface IPaymentProvider {
  createPayment(amount: number, currency: string, description: string);
}

@Injectable()
export class DonationService {
  private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

  constructor(
    private readonly yookassaService: YookassaService,
    private readonly stripeService: StripeService,
  ) {
    this.providers.set(PaymentProviderType.YOOKASSA, this.yookassaService);
    this.providers.set(PaymentProviderType.STRIPE, this.stripeService);
  }

  async createPayment(body: CreatePaymentDto) {
    const provider = this.providers.get(body.type);

    if (!provider) {
      throw new BadRequestException('Выбранный способ оплаты не поддерживается.');
    }

    let currency: string;
    const description = `Пожертвование на сумму ${body.sum}`;

    if (body.type === PaymentProviderType.STRIPE) {
      currency = 'EUR';
    } else if (body.type === PaymentProviderType.YOOKASSA) {
      currency = 'RUB';
    }

    return provider.createPayment(body.sum, currency, description);
  }
}