import { Body, Controller, Get, Post, Query, Req, Res, UseGuards } from '@nestjs/common';
import { PhotoService } from './photo.service';
import {Auth} from "@/api/user/decorators/auth.decorator";
import { OptionalJwtAuthGuard } from '@/api/user/guards/auth.optional.guard';
import { User } from '@/api/user/decorators/user.decorator';

@Controller('client/photo')
export class PhotoController {
  constructor(private readonly photoService: PhotoService) {}

  @Get()
  @UseGuards(OptionalJwtAuthGuard)
  async getAll(
    @Query('lang') lang: string,
    @User() user: any
  ) {
    return await this.photoService.getAll(lang, user?.id);
  }

  @Get('favourites')
  @Auth()
  async getFavourites(
    @Req() req: any,
    @Query('all') all: boolean,
    @Query('page') page: number,
  ) {
    return await this.photoService.getFavourites(req.user.id, all, page);
  }

  @Post('favourites')
  @Auth()
  async addToFavourites(@Body('id') id: number, @Req() req: any) {
    return await this.photoService.addToFavourites(req.user, id)
  }

  @Post('like')
  @Auth()
  async like(
    @Body('id') id: number,
    @Req() req: any,
  ) {
    return await this.photoService.like(id, req.user)
  }

}
