import { Injectable } from '@nestjs/common';
import {FileSystemStoredFile} from "nestjs-form-data";
import {File} from '@/entity/File'
import {basename, dirname, join, extname} from "path";
import {existsSync, unlinkSync, mkdirSync, renameSync, rmSync, copyFileSync} from 'fs'
import {Content} from "@/entity/Content";
import * as sharp from 'sharp';
import slugify from 'slugify';
import {PhotoTranslation} from "@/entity/PhotoTranslation";
import {Photo} from "@/entity/Photo";

@Injectable()
export class FileService {
    private readonly UPLOAD_ROOT = join(__dirname, '../../../upload');

    async save(
        originalName: string,
        folder: string,
        tmpFileName: string,
        oldFile: any = null,
        description: any = ''
    ): Promise<File> {
        const uploadDir = join(this.UPLOAD_ROOT, folder);
        const tmpFilePath = join(this.UPLOAD_ROOT, tmpFileName);
        const newFileName = `${folder}/${basename(tmpFileName)}`;
        const newFilePath = join(this.UPLOAD_ROOT, newFileName);

        try {
            // 1. Создаем директорию, если нужно
            if (!existsSync(uploadDir)) {
                mkdirSync(uploadDir, { recursive: true });
            }

            // 2. Проверяем, что временный файл существует
            if (!existsSync(tmpFilePath)) {
                throw new Error(`Temporary file not found: ${tmpFilePath}`);
            }

            // 3. Копируем файл в новое место
            copyFileSync(tmpFilePath, newFilePath);

            // 4. Проверяем, что файл скопировался
            if (!existsSync(newFilePath)) {
                throw new Error(`Failed to copy file to: ${newFilePath}`);
            }

            // 5. Сохраняем запись в базе
            const fileRecord = await File.save({
                name: newFileName,
                originalName,
                description
            });

            // 6. Только после успешного сохранения удаляем старый файл
            if (oldFile?.name) {
                const oldFilePath = join(this.UPLOAD_ROOT, oldFile.name);
                if (existsSync(oldFilePath)) {
                    rmSync(oldFilePath);
                }
            }

            return fileRecord;
        } catch (error) {
            // В случае ошибки удаляем частично созданные файлы
            if (existsSync(newFilePath)) {
                rmSync(newFilePath);
            }
            throw error;
        }
    }

    // async save(file: FileSystemStoredFile, folder: string, name: string) {
    //     const uploadPath = join(__dirname, '../../../', 'upload', folder);
    //     if (!existsSync(uploadPath)) {
    //         mkdirSync(uploadPath, { recursive: true });
    //     }
    //
    //     let path = file.path
    //     let time = (new Date().getTime() / 1000).toFixed(0)
    //     let slug = `${time}_${slugify(decodeURIComponent(name), {lower: true})}`
    //     let filePath = join(uploadPath, slug);
    //
    //     renameSync(path, filePath)
    //
    //     filePath = await this.resizeImage(filePath)
    //
    //     return await File.save({
    //         name: `${folder}/${basename(filePath)}`,
    //         originalName: decodeURIComponent(name)
    //     })
    // }

    async resizeImage(filePath: string) {
        if(['.png', '.jpg', '.jpeg'].includes(extname(filePath))) {
            await sharp(filePath).webp({
                quality: 80,
                lossless: false,
                alphaQuality: 90,
                effort: 6
            }).toFile(filePath.replace(extname(filePath), '.webp'));
            if(existsSync(filePath)) {
                rmSync(filePath);
            }
            filePath = filePath.replace(extname(filePath), '.webp')
        }
        return filePath
    }

    async update(id: number, body: any) {
        return await File.update(id, body)
    }

    async delete(id: number) {
        const file = await File.findOne({
            where: {id},
            relations: ['content', 'photoTranslations'],
        })
        if(!file) return false;
        if(file.content) {
            await Content.update(file.content.id, {preview: null})
        }
        if(file.photoTranslations) {
            for(let item of file.photoTranslations) {
                await PhotoTranslation.update(item.id, {cover: null})
            }
        }
        // if(file.coverTranslation) {
        //     await PhotoTranslation.update(file.coverTranslation.id, {cover: null});
        // }
        const path = dirname(__filename) + '/../../../upload/' + file.name;
        if(existsSync(path)) {
            rmSync(path)
        }
        return await File.delete(id)
    }

    async deleteByPath(path: string, folder: string) {
        const uploadIndex = path.indexOf('/upload');
        const result = '.' + path.substring(uploadIndex);

        if(existsSync(result)) {
            return unlinkSync(result)
        }

        return false
    }

    async changeSort(items: any) {
        for(let item of items) {
            await File.update(item.id, {sort: item.sort})
        }
    }
}
