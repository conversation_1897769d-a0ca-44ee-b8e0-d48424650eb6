<div class="forgot-page flex flex-col gap-[32px] justify-center items-center">
  @if(!token) {

    @if(linkSent) {
      <div class="forgot-title">Ссылка для восстановления пароля отправлена на почту {{resetForm.value.email}}</div>
      <img src="assets/images/login-icon.svg" alt="forgot-icon" width="336" height="38">
    } @else {
      <div class="forgot-title">Восстановление пароля</div>
      <img src="assets/images/login-icon.svg" alt="forgot-icon" width="336" height="38">
      <form class="flex flex-col gap-[20px]" [formGroup]="resetForm" (ngSubmit)="resetFormSubmit()">
        <div class="form-control">
          <label for="email">Введите ваш E-mail</label>
          <input
            id="email"
            formControlName="email"
            type="email"
          />
        </div>

        <div *ngIf="email?.invalid" class="message-box">
          Введите корректный email.
        </div>
        <button class="submit-button" type="submit" [disabled]="resetForm.invalid">Отправить</button>
      </form>
    }
  } @else {
    <div class="forgot-title">Смена пароля</div>
    <img src="assets/images/login-icon.svg" alt="forgot-icon" width="336" height="38">
    <form class="flex flex-col gap-[20px]" [formGroup]="changePasswordForm" (submit)="changePasswordFormSubmit()">
      <div class="form-control">
        <label for="password">Введите пароль</label>
        <input type="text" formControlName="password">
      </div>
      <div class="form-control">
        <label for="confirmPassword">Повторите пароль</label>
        <input type="text" formControlName="confirmPassword">
      </div>

      <div style="color: red;" *ngIf="changePasswordForm.value.password != changePasswordForm.value.confirmPassword">Пароли не совпадают</div>

      <input type="hidden" formControlName="token">
      <button class="submit-button" type="submit" [disabled]="changePasswordForm.invalid">Изменить пароль</button>
    </form>
  }

</div>

