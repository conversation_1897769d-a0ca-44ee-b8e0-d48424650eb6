import {FormBuilder, ReactiveFormsModule, Validators} from "@angular/forms";
import {passwordMatchValidator} from "@/validators/validators";
import {AuthService} from "@/services/auth.service";
import {Component, inject} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import {CommonModule} from "@angular/common";
import {Router} from "@angular/router";
import {ToasterService} from "@/services/toaster.service";

@Component({
  selector: 'app-forgot',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule],
  templateUrl: './forgot.component.html',
  styleUrl: './forgot.component.scss'
})
export class ForgotComponent {
  authService = inject(AuthService)
  route = inject(ActivatedRoute)
  fb = inject(FormBuilder)
  router = inject(Router)
  toasterService = inject(ToasterService)
  resetForm = this.fb.group({
    email: ['', [Validators.required, Validators.email]]
  })
  changePasswordForm = this.fb.group({
    password: ['', [Validators.required]],
    confirmPassword: ['', [Validators.required]],
    token: ['', [Validators.required]]
  }, {validators: passwordMatchValidator})
  successMessage: string | null = null;
  errorMessage: string | null = null;
  token: string | null = null;
  linkSent: boolean = false

  ngOnInit() {
    this.route.queryParamMap.subscribe(params => {
      this.token = params.get('token')
      this.changePasswordForm.patchValue({token: this.token})
    })
  }

  resetFormSubmit() {
    if(!this.resetForm.valid) return;
    if(this.resetForm.value.email) {
      this.authService.resetPassword(this.resetForm.value.email).subscribe(() => {
        this.linkSent = true
      })
    }
  }

  changePasswordFormSubmit() {
    if(!this.changePasswordForm.valid) return;
    this.authService.changePassword(this.changePasswordForm.value).subscribe(() => {
      this.toasterService.showToast('Пароль успешно изменен', 'success', 'bottom-middle', 1000);
      setTimeout(() => this.router.navigate(['/signin']), 2000)
    })
  }

  get email() {
    return this.resetForm.get('email')
  }
}
