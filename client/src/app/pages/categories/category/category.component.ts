import {Component, HostListener, ElementRef, inject, PLATFORM_ID, ViewChild, effect, DestroyRef} from '@angular/core';
import {ContentService} from "@/services/content.service";
import {DatePipe, isPlatform<PERSON>rowser, Ng<PERSON><PERSON>, NgIf, NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {TranslocoService} from "@jsverse/transloco";
import {ActivatedRoute, Router} from "@angular/router";
import {FormBuilder, FormGroup, ReactiveFormsModule} from "@angular/forms";
import {ReadingTimePipe} from "@/pipes/reading-time.pipe";
import {ToasterService} from "@/services/toaster.service";
import {AudioService} from "@/services/audio.service";
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';
import { AuthService } from '@/services/auth.service';
import { ShareDataService } from '@/services/share-data.service';
import { ProfileService } from '@/services/profile.service';
import { CustomDropdownComponent } from '@/components/custom-dropdown/custom-dropdown.component';
import {combineLatest, debounceTime, distinctUntilChanged, take} from "rxjs";
import { formatLectureCountMessage } from '@/utils/pluralization.util';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-category',
  standalone: true,
  imports: [
    NgIf,
    NgClass,
    NgOptimizedImage,
    ReactiveFormsModule,
    DatePipe,
    ReadingTimePipe,
    BreadcrumbComponent,
    CustomDropdownComponent
  ],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss'
})
export class CategoryComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;

  contentService = inject(ContentService);
  translocoService = inject(TranslocoService);
  router = inject(Router);
  route = inject(ActivatedRoute);
  fb = inject(FormBuilder)
  toasterService = inject(ToasterService);
  profileService = inject(ProfileService);
  audioService = inject(AudioService);
  auth = inject(AuthService)
  shareDataService = inject(ShareDataService);
  platformId = inject(PLATFORM_ID);
  destroyRef = inject(DestroyRef);
  categories: any = []
  tags: any = []
  show_menu: Record<number, boolean> = {};
  filter: FormGroup = this.fb.group({
    search: [''],
    category: [null],
    sortOrder: ['dateDesc'],
    tag: [[]],
    page: [1]
  });
  id = this.route.snapshot.params['id']
  tag = this.route.snapshot.queryParams['tag']
  categoryName: string = '';
  selectedCategory: number = -1;
  dropdownOpen = false;
  data: any = [];
  // catDropdownOpen = false;
  // tagDropdownOpen = false;
  // selectedCatLabel = '';
  // selectedTagLabel = '';
  selectedSortLabel = 'Алфавиту';
  sortOptions = [
    { label: 'Алфавиту', value: 'title', id: 0 },
    { label: 'Дате', value: 'date', id: 1 },
    { label: 'Популярности', value: 'views', id: 2 }
  ];
  sortDirection: 'Asc' | 'Desc' = 'Asc';
  currentSortField: string = 'title';

  isOpened: Record<number, boolean> = {};
  queryTags = [];
  selectedTags: any[] = [];

  @HostListener('document:mousedown', ['$event'])
  onClickOutside(event: MouseEvent) {
    const modal = this.modal?.nativeElement as HTMLDialogElement;
    if (modal && modal.open) {
      const dialogContent = modal.querySelector('.cont_mod');
      if (dialogContent && !dialogContent.contains(event.target as Node) &&
          !(event.target as HTMLElement).classList.contains('x_bt')) {
        this.closeModal(modal);
      }
    }
  }

  resetFilters() {
    // for (let key in this.filters) this.filters[key] = '';
    // this.filters.page = 1;
    // this.libraryService.applyFilters(this.filters);
  }

  ngOnInit() {
    const categories$ = this.contentService.getCategories();
    const tags$ = this.audioService.getTags();
    const params$ = this.route.params.pipe(take(1));
    const queryParams$ = this.route.queryParams.pipe(take(1));

    combineLatest([categories$, tags$, params$, queryParams$]).subscribe(([categories, tags, params, queryParams]) => {
      this.categories = categories;
      this.tags = tags;

      const categoryId: any = params['id'] ? Number(params['id']) : null;
      const tagIds = queryParams['tags'] ? queryParams['tags'].split(',').map((id: any) => Number(id)) : [];

      this.id = categoryId;
      this.categoryName = this.categories.find((c: any) => c.id == this.id)?.title || 'Все категории';
      this.selectedCategory = categoryId;

      this.filter.patchValue({
        category: categoryId,
        tag: tagIds
      }, { emitEvent: false });

      this.updateSelectedTagsFromForm();
      this.updateSortLabels();

      // Initial load - don't show toast notification
      this.contentService.setFilterOperation(false);
      this.applyFilter();
    });

    this.filter.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr))
    ).subscribe(formValue => {
      this.filter.patchValue({ page: 1 }, { emitEvent: false });

      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { tags: formValue.tag && formValue.tag.length > 0 ? formValue.tag.join(',') : null },
        queryParamsHandling: 'merge',
      });

      this.applyFilter();
    });

    // Subscribe to filter results for toast notifications
    this.contentService.filterResults$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(({total, isFilterOperation}) => {
      if (isFilterOperation) {
        const message = formatLectureCountMessage(total);
        this.toasterService.showToast(message, 'default', 'bottom-middle', 4000);
      }
    });
  }

  updateSortLabels() {
    const sortValue = this.filter.get('sortOrder')?.value || '';
    const match = sortValue.match(/^(.*?)(Asc|Desc)?$/);
    this.currentSortField = match?.[1] || 'date';
    this.sortDirection = (match?.[2] as 'Asc' | 'Desc') || 'Desc';
    this.selectedSortLabel = this.sortOptions.find(option => option.value === this.currentSortField)?.label || 'Дате';
  }

  updateSelectedTagsFromForm() {
    const tagIds = this.filter.get('tag')?.value || [];
    this.selectedTags = this.tags.filter((tag: any) => tagIds.includes(tag.id));
  }

  setTags() {
    this.selectedTags = [];

    this.queryTags.forEach((tagId: string) => {
      const selectedTag = this.tags.find((t: any) => t.id == tagId);

      if (selectedTag) {
        this.selectedTags.push(selectedTag);
      }
    });
  }

  navigate(article: any) {
    this.router.navigate(['/' + this.translocoService.getActiveLang() + '/categories/' + article.category.id + '/' + article.slug])
  }

  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  selectSort(field: string) {
    if (this.currentSortField === field) {
      this.sortDirection = this.sortDirection === 'Asc' ? 'Desc' : 'Asc';
    } else {
      this.currentSortField = field;
      this.sortDirection = 'Asc';
    }
    const sortOrder = this.currentSortField + this.sortDirection;
    this.filter.patchValue({ sortOrder });
    this.updateSortLabels();
    this.dropdownOpen = false;
  }

  applyFilter(loadMore: boolean = false) {
    if (!loadMore) {
      this.filter.patchValue({ page: 1 }, { emitEvent: false });
      // Set filter operation flag for toast notifications (only for new filters, not pagination)
      this.contentService.setFilterOperation(true);
    } else {
      // For loadMore operations, don't show toast
      this.contentService.setFilterOperation(false);
    }
    this.contentService.getAll(this.filter.value, loadMore).subscribe();
  }

  loadMoreContent() {
    if (this.contentService.hasMorePages()) {
      const currentPage = this.filter.get('page')?.value;
      this.filter.patchValue({ page: currentPage + 1 }, { emitEvent: false });
      this.contentService.getAll(this.filter.value, true).subscribe();
    }
  }

  addTagFilter(tag: any) {
    if (!this.selectedTags.some(t => t.id === tag.id)) {
      this.selectedTags.push(tag);
      this.updateUrlWithTags();
    }
  }

  get hasMoreContent(): boolean {
    return this.contentService.hasMorePages();
  }

  get isLoading(): boolean {
    return this.contentService.isLoading();
  }

  share(content: any) {
    if (isPlatformBrowser(this.platformId)) {
      const lang = this.translocoService.getActiveLang()
      navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/test/${content.slug}`).then(() =>
        this.toasterService.showToast('Ссылка на страницу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
      )
    }
  }

  openModal() {
    this.modal.nativeElement.showModal();
    this.updateSelectedTagsFromForm();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  toggleTagSelection(selectedTagObjects: any[]) {
    const selectedTagIds = Array.isArray(selectedTagObjects)
      ? selectedTagObjects.map(tag => tag.id)
      : [];

    this.filter.get('tag')?.setValue(selectedTagIds);

    this.selectedTags = selectedTagObjects;
  }



  // Update the updateUrlWithTags method to handle removing the last tag
  updateUrlWithTags() {
    // Create query params object
    const queryParams: any = {};

    // Add tags if there are selected tags
    if (this.selectedTags.length > 0) {
      // Convert array of tag objects to array of tag IDs
      const tagIds = this.selectedTags.map(tag => tag.id);
      // Join tag IDs with comma for URL
      queryParams.tags = tagIds.join(',');
    } else {
      // If no tags are selected, explicitly set tags parameter to null
      // This will remove the 'tags' parameter from the URL
      queryParams.tags = null;
    }

    // Navigate to the same route but with updated query parameters
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: queryParams,
      queryParamsHandling: 'merge', // Keep other query params if they exist
    });
  }


   // Метод для проверки, выбран ли тег
   isTagSelected(tagId: number): boolean {
     return this.selectedTags.some(tag => tag.id === tagId);
   }

   // Метод для обновления фильтра по тегам
   updateTagFilter() {
     // Если есть выбранные теги, используем первый (или можно объединить ID через запятую)
     if (this.selectedTags.length > 0) {
       this.filter.patchValue({ tag: this.selectedTags.map((e: any) => e.id) });
     } else {
       this.filter.patchValue({ tag: [] });
     }

     // Применяем фильтр
     this.applyFilter();
   }

  removeTag(tagToRemove: any) {
    const currentTags: number[] = this.filter.get('tag')?.value || [];
    const updatedTags = currentTags.filter(id => id !== tagToRemove.id);
    this.filter.get('tag')?.setValue(updatedTags);
    this.updateSelectedTagsFromForm();
  }

  applyFilters() {
    this.updateSelectedTagsFromForm();
    this.closeModal(this.modal.nativeElement);
  }

  navigateToCategory(categorys: any) {
    this.closeModal(this.modal.nativeElement);
    const categoryId = categorys[0].id;
    this.filter.patchValue({ category: categoryId });
    this.router.navigate(['/' + this.translocoService.getActiveLang() + '/categories/' + categoryId]);
  }

  like(content: any) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.contentService.like(content.id).subscribe({
      next: (r) => {

        content.liked = !content.liked;
        if(content.liked) content.likes++
        else content.likes--;

        if(!r) {
          this.toasterService.showToast('Статья добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Статья удалена из понравившихся!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  favorites(content: any) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.contentService.addToFavourites(content.id).subscribe({
      next: (r) => {

        content.inFavourites = !content.inFavourites;

        if(!r) {
          this.toasterService.showToast('Статья добавлена в избранное!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Статья удалена из избранного!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  isLiked(content: any) {
     return content.liked
  }

  isInFavourites(content: any) {
    return content.favourites.some((e: any) => e.user.id == this.profileService.profile?.id)
  }


  showMenu(index: number, event: Event) {
    event.stopPropagation();

    this.show_menu = {};
    this.show_menu[index] = !this.show_menu[index];
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedElement = event.target as HTMLElement;

    const hasOpenMenu = Object.values(this.show_menu).some(isOpen => isOpen);

    if (hasOpenMenu) {
      const isMenuToggleClick = clickedElement.closest('.actions_w');

      if (!isMenuToggleClick) {
        this.show_menu = {};
      }
    }
  }

  protected readonly environment = environment;
}
