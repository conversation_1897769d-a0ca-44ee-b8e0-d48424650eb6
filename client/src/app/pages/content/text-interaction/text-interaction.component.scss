.html_wrap {
    #content {
        overflow-wrap: break-word;
        white-space: normal;
        position: sticky;

        // Red text selection highlight for copy functionality
        ::selection {
            background-color: var(--selection);
            // color: white;
        }

        ::-moz-selection {
            background-color: var(--selection);
            // color: white;
        }

        // Ensure selection works on all child elements
        * {
            ::selection {
                background-color: var(--selection);
                // color: white;
            }

            ::-moz-selection {
                background-color: var(--selection);
                // color: white;
            }
        }
    }
}

button.fake-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.library-context {
    // Smooth transitions for positioning
    transition: opacity 0.2s ease, transform 0.2s ease;
    transform-origin: center top;

    // Ensure proper z-index for library context
    &.library-type {
        z-index: 1001;
    }

    span {
        display: block;
        font-family: Prata;
        font-weight: 400;
        font-size: 18px;
        line-height: 18px;
        color: var(--font-color1);
        width: 50%;
        padding: 5px 0 5px 10px;
    }
}

.quote {
  background: var(--selection);
  color: black;
}
