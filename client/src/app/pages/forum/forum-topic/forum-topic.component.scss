@import url(../forum.component.scss);
@import url(../../lecture/lecture.component.scss);

.wrapper_line {
  max-width: 1170px;
  padding: 20px 0 165px 0;
}

.dec_head-title_ {
  padding: 14px 0 42px 0 !important;
}

.forum-category {
  margin-top: 60px;
}

.forum-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px;
  padding: 18px 28px;
  background: var(--selection);
}

.icons_w {
  margin-right: 30px;
}

.topic-user__avatar {
  width: 43px;
  height: 43px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 15px;
  margin-right: 15px;
  color: var(--font-color1);
}

.topic-user {
  display: flex;
}

.topic-user__avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.topic-title {
  font-weight: 500;
  font-size: 18px
}

.topic-user_ {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 17px;
  color: var(--font-color1);
}

.topic-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 58px);
}

.topic-images {
  display: flex;
  flex-wrap: wrap;
}

.topic-images div {
  flex-basis: 180px;
  padding: 15px;
}

.topic-images img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.topic-content {
  padding: 30px 40px;

  span {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: var(--font-color1);
  }
}

.comment-reply {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 25px;
  color: var(--text-color);
  text-decoration: underline;
  cursor: pointer;
}

.topic-comment__footer {
  display: flex;
  padding-left: 40px;
}

.div_sep {
  width: 100%;
  border-top: 1px solid var(--book_about);
  margin: 40px 0;
}

.reply___ {
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  color: var(--font-color1);
  border: 1px solid var(--book_about);
  border-bottom: none;
  padding: 30px 30px 0 30px;
}

.editor-toolbar {
  display: flex;
  padding: 25px 30px 15px 30px;
}

// .comment-form textarea {
//   width: 100%;
//   background: #eaeaea;
//   outline: none;
//   padding: 13px;
//   border-radius: 4px;
// }

.reply {

  .forum-header {
    height: fit-content;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .topic-content {
    background: var(--selection);
    padding: 5px 40px 30px 40px;
  }

  .topic-comment__footer {
    background: var(--selection);
    padding-bottom: 40px;
  }
}

.editor-content:empty:before {
  content: attr(placeholder);
  color: var(--font-color1);
}

.editor-content img {
  display: block;
  max-width: 100%;
  margin: 10px 0;
  border-radius: 4px;
}

.editor-content {
  min-height: 120px;
  outline: none;
  border-top: 1px solid var(--book_about);
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  color: var(--font-color1);
  padding: 15px 30px 90px 30px;
}

.simple-editor {
  min-height: 275px;
  border-width: 1px;
  border-bottom-right-radius: 20px;
  border-bottom-left-radius: 20px;
  border: 1px solid var(--book_about);
  border-top: none;
  overflow: hidden;
}

.editor-toolbar button {
  margin-right: 22px;
  cursor: pointer;
}

.smile_ {
  position: absolute;
  right: 30px;
  bottom: 11px;
  cursor: pointer;
}

.comment-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: -70px;

  button {
    margin-right: 30px;
  }
}

/////////////////////////////////////////////

.forum-table {
  padding: 15px;
}

.forum-table table {
  background: white;
}

.topic-actions {
  font-size: 12px;
}

.comment-remove {
  color: #f11;
  cursor: pointer;
}

.comment-edit {
  cursor: pointer;
}

.topic-actions a {
  margin-left: 10px;
  cursor: pointer;
}

.cancel-reply {
  margin-left: 10px;
  color: #666;
  cursor: pointer;
  text-decoration: underline;
}

.cancel-reply:hover {
  color: #333;
}

.active-format {
  background-color: #6b6b6b !important;
  border-color: #565555 !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .forum-header {
    border-radius: 15px;
    padding: 9px 20px;
  }

  .topic-info {
    width: calc(100% - 50px);
  }

  .icons_w {
    display: flex !important;
  }

  .topic-user__avatar {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }

  .topic-content {
    padding: 20px 25px;

    span {
      font-size: 16px;
      line-height: 21px;
    }
  }

  .topic-comment__footer {
    padding-left: 25px;
  }

  .div_sep {
    margin: 30px 0;
  }

  .reply___ {
    font-size: 17px;
    padding: 25px 20px 0 20px;
  }

  .editor-toolbar {
    padding: 15px 20px;

    button {
      margin-right: 20px;
    }
  }

  .editor-content {
    font-size: 16px;
    line-height: 21px;
    padding: 20px 20px 90px 20px;
  }

  .smile_ {
    right: 20px;
    bottom: 15px;

    svg {
      width: 20px;
      height: 20px;
    }
  }

  .comment-btn {
    margin-top: -60px;

    button {
      margin-right: 20px;
    }
  }

  .audio_chip {
    height: 34px;
    font-size: 15px;
  }
}

@media (max-width: 570px) {
  .topic-info {
    flex-direction: column;
    align-items: unset;
  }

  .topic-user_ {
    font-size: 14px;

    &:last-child {
      font-size: 11px;
    }
  }

  .topic-content {
    padding: 15px 10px;

    span {
      font-size: 15px;
      line-height: 20px;
    }
  }

  .icons_w {
    margin-right: 20px;
  }

  .comment-reply {
    font-size: 15px;
    line-height: 20px;
  }

  .div_sep {
    margin: 20px 0 25px 0;
  }

  .topic-comment__footer {
    padding-left: 10px;
  }

  .reply___ {
    font-size: 15px;
    padding: 16px 10px 0 10px;
  }

  .comment-form .editor-toolbar button {
    font-size: 16px;
  }

  .editor-toolbar {
    padding: 15px 10px;
  }

  .smile_ {
    right: 15px;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .editor-content {
    font-size: 15px;
    line-height: 18px;
    padding: 15px 10px 80px 10px;
  }

  .audio_chip {
    height: 30px;
    font-size: 13px;
  }

  .comment-btn button {
    margin-right: 15px;
  }

  .comment-btn {
    margin-top: -45px;
  }
}

@media (max-width: 420px) {
  .cat_wrap {
    margin: -60px auto 20px auto !important;
  }
}