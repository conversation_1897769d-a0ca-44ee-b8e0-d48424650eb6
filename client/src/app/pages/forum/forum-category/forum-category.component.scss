@use "../../categories/category/category.component.scss";

.art_img {
  width: 90px;
  height: 90px;
  min-width: 90px;

  img {
    width: 90px;
  }
}

.titl_w {
  margin-left: 30px;
}

.articles-search input {
  max-width: 100%;
}

input {
  max-width: 460px;
  width: 100%;
  border: 1px solid var(--text-color);
  padding: 0 15px 0 25px;
  cursor: pointer;
  border-radius: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  color: var(--font-color1);

  &:focus {
    outline: none;
  }

  &::placeholder {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: var(--font-color1);
  }
}

textarea {
  max-width: 460px;
  width: 100%;
  border: 1px solid var(--text-color);
  padding: 10px;
  cursor: pointer;
  border-radius: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100px;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  color: var(--font-color1);

  &:focus {
    outline: none;
  }

  &::placeholder {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: var(--font-color1);
  }
}

.article-item {
  padding: 18px 0;
  height: 130px;
  max-height: unset;
}

.article-category {
  line-height: 21px;
}

.info_bl {
  display: flex;
  align-items: baseline;
  padding-top: 15px;

  span {
    display: flex;
    align-items: center;
    font-family: Prata;
    font-weight: 400;
    font-size: 15px;
    line-height: 18px;
    color: var(--text-color);
    white-space: nowrap;

    img {
      margin: -4px 8px 0 34px;
    }
  }
}

.article-title.ov_wrap {
  -webkit-line-clamp: 2;
}

.ar_wrapp {
  margin-top: 40px;
}

.article-item:last-child {
  border-bottom: 1px solid rgb(222, 165, 61);
}

.p_filter {
  right: 80px;
}

.p_filter::before {
  content: "";
  background-image: url(assets/images/icons/pluss.svg);
}

.articles-search {
  background: url(assets/images/strct.svg);
  height: 80px;
  background-position: center !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
}

.custom-file-upload {
  display: inline-block;
  position: relative;
  max-width: 460px;

  input[type="file"] {
    display: none;
  }

  label {
    transition: all 0.3s ease;
    gap: 10px;
    width: 100%;
    border: 1px solid var(--text-color);
    padding: 0 15px 0 25px;
    cursor: pointer;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    font-family: Prata;
    font-weight: 400;
    font-size: 17px;
    line-height: 24px;
    color: var(--font-color1);
    margin-top: 20px;

    // &:hover {
    //   background: linear-gradient(to right, #fdf8f1, #fdf8f1) padding-box,
    //     linear-gradient(to right, #c89347, #dfb66e) border-box;
    // }

    .icon {
      background: url(assets/images/icons/pluss.svg);
      width: 14px;
      height: 14px;
      background-size: 14px;
    }
  }
}

.preview-wrapper {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.preview-card {
  background-color: var(--selection);
  border-radius: 15px;
  padding: 10px;
  width: 182px;
  min-height: 172px;
  position: relative;
  text-align: center;
}

.file-name {
  font-family: Prata;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: var(--font-color1);
  margin-top: 10px;
  word-break: break-all;
}

.preview-card img {
  width: 100%;
  height: 98px;
  object-fit: cover;
  border-radius: 12px;
}

.remove-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--selection);
  border: none;
  color: var(--text-color);
  /* font-size: 18px; */
  width: 32px;
  cursor: pointer;
  border-radius: 50%;
  height: 32px;

  span {
    display: flex;
    width: 100%;
    height: 100%;
    background: var(--x_bt);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 16px;
  }
}

.save-btn {
  margin: 50px 20px 58px 0 !important;
}

.save-btn:last-of-type {
  margin: 50px 0 58px 0 !important;
}

dialog.stylized_wide .cont_mod {
  width: 75%;
  max-width: 460px;
}

@media (max-width: 940px) {
  .dbl_wr {
    flex-direction: column;
  }

  .art_img {
    width: 60px;
    height: 60px;
    min-width: 60px;

    img {
      width: 60px;
    }
  }

  .titl_w {
    margin-left: 20px;
  }

  .info_bl span img {
    margin: -4px 8px 0 20px;
  }

  .p_filter {
    right: 10%;
    font-size: 18px;
  }

  .articles-search input {
    font-size: 18px;

    &::placeholder {
      font-size: 18px;
    }
  }

  .article-title {
    font-size: 20px;
    line-height: 24px;
    margin-bottom: 6px;
  }

  .article-category {
    font-size: 14px;
  }

  .info_bl {
    padding-top: 10px;

    span {
      font-size: 14px;
      line-height: 14px;

      img {
        width: 20px;
      }
    }
  }

  .article-item {
    min-height: 120px;
    height: 120px;
  }
}

@media (max-width: 768px) {
  .wrapper_line {
    padding: 90px 79px 100px 79px;
  }

  .p_filter {
    right: 7%;
    font-size: 15px;
  }

  .preview-wrapper {
    gap: 16px;
  }

  .preview-card {
    padding: 8px;
    width: 145px;
    min-height: 137px;
  }

  .file-name {
    margin-top: 8px;
  }

  .preview-card img {
    height: 78px;
  }

  .remove-btn {
    width: 26px;
    height: 26px;

    span {
      background-size: 13px;
    }
  }
}

@media (max-width: 720px) {
  .wrapper_line {
    padding: 90px 0 100px 0;
  }

  .p_filter {
    font-size: 18px;
  }
}

@media (max-width: 640px) {
  .p_filter {
    font-size: 15px;
  }

  dialog.stylized_wide .cont_mod {
    width: 100%;
    max-width: fit-content;
    margin: 0 auto;
  }
}

@media (max-width: 550px) {
  .articles-search {
    background: url(assets/images/strct_.svg);
  }

  .p_filter {
    right: 0;
    opacity: 0;
  }

  .articles-search input {
    padding: 5px 30% 5px 13%;
  }

  .middle_stripe {
    padding: 0 25px;
  }

  .art_img {
    width: 50px;
    height: 50px;
    min-width: 50px;

    img {
      width: 50px;
    }
  }

  .article-item {
    padding: 14px 0;
    min-height: 109px;
    height: 109px;
  }

  .titl_w {
    margin-left: 10px;
  }

  .article-title {
    font-size: 15px;
    line-height: 17px;
    margin-bottom: 3px;
    max-width: 100%;
  }

  .article-category {
    font-size: 11px;
    line-height: 21px;

    &.ml-6 {
      margin-left: 0;
    }
  }

  .rticle-category {
    flex-direction: column;
  }

  .info_bl {
    padding-top: 7px;

    span {
      font-size: 11px;
      line-height: 11px;

      img {
        margin: -4px 8px 0 10px;
        width: 15px;
      }
    }
  }

  .articles-search input {
    font-size: 17px;

    &::placeholder {
      font-size: 17px;
    }
  }

  .ar_wrapp {
    margin-top: 25px;
  }

  .preview-wrapper {
    gap: 20px;
  }

  .preview-card {
    width: 139px;
    min-height: 131px;
  }

  .preview-card img {
    height: 75px;
  }

  .remove-btn {
    width: 25px;
    height: 25px;

    span {
      background-size: 12px;
    }
  }
}

@media (max-width: 370px) {
  .middle_stripe {
    padding: 0 18px;
  }
}