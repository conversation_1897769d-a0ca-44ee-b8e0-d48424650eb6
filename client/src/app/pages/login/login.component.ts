import { FormBuilder, ReactiveFormsModule, Validators } from "@angular/forms";
import { ActivatedRoute, Router, RouterModule } from "@angular/router";
import { AuthService } from "@/services/auth.service";
import { Component, ElementRef, inject, ViewChild } from '@angular/core';
import { environment } from "@/env/environment";
import { ModalService } from "@/services/modal-service.service";
import { NgOptimizedImage } from "@angular/common";

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ReactiveFormsModule, RouterModule, NgOptimizedImage],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  authService = inject(AuthService)
  modalService = inject(ModalService)
  fb = inject(FormBuilder)
  router = inject(Router)
  route = inject(ActivatedRoute)
  return = this.route.snapshot.queryParams['return']
  form = this.fb.group({
    email: ['', Validators.required],
    password: ['', Validators.required]
  })
  showPasswordState: boolean = false;
  modalMessage = '';

  ngOnInit() {
    this.modalService.modalTrigger$.subscribe(message => {
      this.modalMessage = message;
      this.modal.nativeElement.showModal();
    });
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  signIn() {
    this.authService.signIn(this.form.value).subscribe(() => {
      if(this.return) this.router.navigateByUrl(this.return);
    })
    return false;
  }

  signInGoogle() {
    location.href = environment.serverUrl + '/api/user/google'
  }

  toRegisterPage() {
    this.router.navigate(['/ru/signup'], { queryParams: this.return ? { return: '/ru/anketa' } : {} });
  }

  showPassword() {
    this.showPasswordState = !this.showPasswordState;
  }
}
