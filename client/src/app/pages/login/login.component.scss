@import url(../categories/category/category.component.scss);

.auth_head {
    line-height: 21px;
    text-align: center;
}

.login-page {
    height: 843px;
    max-width: 490px;
    padding: 0 20px;
    margin: 0 auto;

    .login-title {
        font-family: BeaumarchaisC;
        font-weight: 400;
        font-size: 50px;
        line-height: 50px;
        letter-spacing: 0;
        text-align: center;
        vertical-align: middle;
        color: var(--font-color1);
        padding-bottom: 16px;
    }

    form {
        width: 100%;

        .form-control {
            display: flex;
            flex-direction: column;
            gap: 6px;

            label {
                font-family: Prata;
                font-weight: 400;
                font-size: 14px;
                line-height: 14px;
                letter-spacing: 0;
                color: var(--text-color);
            }

            .input-wrap {
                position: relative;

                input {
                    padding-right: 50px;
                }

                .password-suffix {
                    position: absolute;
                    background: url(assets/images/icons/hide.svg);
                    width: 28px;
                    height: 21px;
                    cursor: pointer;
                    top: 50%;
                    transform: translateY(-50%);
                    right: 15px;

                    &:hover {
                        opacity: 0.7;
                    }
                }
            }

            input {
                width: 450px;
                max-width: 100%;
                height: 50px;
                border-radius: 15px;
                outline: none;
                padding: 13px 25px;
                border: 1px solid var(--text-color);
                background: transparent;
                margin: 0 auto;
                font-family: Prata;
                font-weight: 400;
                font-size: 20px;
                line-height: 24px;
                letter-spacing: 0;
                color: var(--font-color1);
            }
        }

        .button_sign_in {
            background: url(assets/images/login-button_1.svg);
            width: 353px;
            height: 50px;
            cursor: pointer;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 20px;
            letter-spacing: 0;
            text-align: center;
            vertical-align: middle;
            padding: 15px 25px;
            color: var(--font-color1);
            margin: 0 auto;
            margin-top: 40px;
        }

        .button_sign_in_google {
            background: url(assets/images/login-button_2.svg);
            width: 353px;
            height: 50px;
            cursor: pointer;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 20px;
            letter-spacing: 0;
            text-align: center;
            vertical-align: middle;
            padding: 15px 25px;
            color: var(--font-color1);
            margin: 5px auto 12px;

            span {
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
        }

        a {
            display: block;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 24px;
            letter-spacing: 0;
            text-align: center;
            cursor: pointer;
            color: var(--font-color1);
        }
    }
}

@media (max-width: 768px) {
    .login-page {
        zoom: 0.9;
    }
}

@media (max-width: 500px) {
    .login-page {
        zoom: 0.8;
    }
}