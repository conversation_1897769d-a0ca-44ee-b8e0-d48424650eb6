@use "../../audio-gallery/audio-gallery.component.scss";

.art_img img {
  width: 52px;
}

.lik_hov {
  .on_hov {
    left: -47px;
  }
}

.save-btn:last-of-type {
  margin: 60px 0 35px 0 !important;
}

.article-title {
  max-width: 76%;
}

.save-btn {
  width: 218px;
  height: 54px;
  padding: 0;
  position: relative;
  margin: 60px 25px 35px 0 !important;

  .btn-backdrop-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: contain;
  }

  .save-btn-label {
    margin: 0 auto;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    padding: 14px 25px;
    color: var(--font-color1);
  }
}

.tr_descr {
  max-width: 81%;
}

.chckk {
  width: 16px;
  height: 16px;
  background: var(--check);
  margin-top: -2px;
  background-size: contain;
  background-repeat: no-repeat;
}

@media (max-width: 900px) {
  .tr_descr {
    max-width: 100%;
  }

  .article-title {
    max-width: 72%;
  }
}

@media (max-width: 768px) {
  .article-title {
    max-width: 86%;
    min-width: unset;
  }
}

@media (max-width: 570px) {
  .article-title {
    max-width: 82%;
  }

  .filter-buttons-container {
    .save-btn {
      width: 188px;
      height: 44px;
      margin: 40px 25px 30px 0 !important;
    }

    .save-btn:last-of-type {
      margin: 40px 0 30px 0 !important;
    }
  }

  .actions_w.show_menu .icons_w.separatre_ .icon-wrap.like_w svg {
    width: 16px;
    height: 16px;
  }

  .save-btn .save-btn-label {
    font-size: 16px;
    line-height: 16px;
  }
}

@media (max-width: 540px) {
  .filter-buttons-container {
    .save-btn {
      width: 150px;
      height: 40px;
      margin: 20px 20px 20px 0 !important;
    }

    .save-btn:last-of-type {
      margin: 20px 0 20px 0 !important;
    }
  }

  .save-btn .save-btn-label {
    font-size: 14px;
    line-height: 14px;
  }
}

@media (max-width: 500px) {
  .chckk {
    width: 14px;
    height: 14px;
    margin-right: 0;
  }
}

@media (max-width: 420px) {
  .filter-buttons-container .save-btn {
    width: 188px;
    margin: 10px auto !important;

    &:last-of-type {
      margin: 10px auto !important;
    }
  }

  .article-title {
    max-width: 77%;
  }
}


.dropdown-item {
  position: relative;
}

.rotate-up {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}

.rotate-down {
  transform: rotate(0deg);
  transition: transform 0.2s ease;
}

.sort-arrow {
  position: absolute;
  right: 5px;
  top: 10px;
}