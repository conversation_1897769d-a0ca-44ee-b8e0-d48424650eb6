// Additional styles to enhance animations
.toast-container {
  // Ensure smooth transitions for all properties
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);

  // Improve visual quality during animations
  backface-visibility: hidden;
  perspective: 1000px;

  // Ensure proper stacking during animations
  will-change: transform, opacity;
}

// Enhance the close button animation
.close-toast-button {
  transition: all 200ms ease;

  &:hover {
    opacity: 0.7;
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

// Smooth transitions for toast content
.toast-body {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-message {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

// Ensure animations work well with different positions
:host {
  // Improve animation performance
  contain: layout style paint;
}