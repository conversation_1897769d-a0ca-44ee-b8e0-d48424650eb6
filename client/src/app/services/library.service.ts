import {inject, Injectable, signal} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {catchError, EMPTY, tap} from "rxjs";
import {TranslocoService} from "@jsverse/transloco";
import {ProfileService} from "./profile.service";
import {ILibrary, ILibraryList} from "@/interfaces/library";
import { ShareDataService } from './share-data.service';
import {ActivatedRoute, Router} from '@angular/router';
import {Location} from "@angular/common";

@Injectable({
  providedIn: 'root'
})
export class LibraryService {
  translocoService = inject(TranslocoService)
  profileService = inject(ProfileService)
  router = inject(Router)
  http = inject(HttpClient)
  route = inject(ActivatedRoute)
  location = inject(Location);
  list: any = signal<ILibraryList>({
    items: [], total:0, page:0, pages:0, audioTags:[], authors: []
  })
  shareDataService = inject(ShareDataService);
  filters: any = {
    page: 1
  }
  totalPages: number = 1

  get(code: string, views = true) {
    return this.http.get('/client/library/' + code, {params: {views, lang: this.translocoService.getActiveLang()}})
  }

  getAll() {
    // Create clean params object for the HTTP request
    const cleanParams: any = {};

    // Only include valid values
    Object.keys(this.filters).forEach(key => {
      const value = this.filters[key];

      // Skip empty, null, or 'null' string values
      if (value === null || value === undefined || value === '' || value === 'null') {
        return;
      }

      // Skip empty arrays
      if (Array.isArray(value) && value.length === 0) {
        return;
      }

      // Add to params
      cleanParams[key] = value;
    });

    return this.http.get('/client/library', {params: cleanParams }).pipe(
      tap((res: any) => {
        this.totalPages = res.pagination.totalPages;
        this.list.set(res)
      })
    )
  }

  addToFavourites(id: number) {
    return this.http.post('/client/library/favourites', { id }).pipe(
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    );
  }

  like(id: number) {
    return this.http.post('/client/library/likes', {id}).pipe(
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
         return EMPTY;
      })
    );
  }

  addQuoteToFavourites(id: number, quote: string, page: number, share = false) {
    return this.http.post('/client/library/quote', {id, quote, page, share})
  }

  deleteQuote(quote: string) {
    return this.http.post(`/client/library/quote/delete`, {quote}).pipe(
      tap(() => this.profileService.getProfile().subscribe())
    )
  }

  getQuote(id: number) {
    return this.http.get('/client/library/quote', {params: {id}})
  }

  applyFilters(filters: any) {
    // Update internal filters first
    for(let key in filters) {
      if(filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
        this.filters[key] = filters[key];
      } else {
        delete this.filters[key]; // Remove empty values
      }
    }

    // Create a clean query params object
    const queryParams: any = {};

    // Only include non-empty values in the query parameters
    Object.keys(this.filters).forEach(key => {
      const value = this.filters[key];

      // Skip null, undefined, empty strings, and "null" string
      if (value === null || value === undefined || value === '' || value === 'null') {
        return;
      }

      // Skip empty arrays
      if (Array.isArray(value) && value.length === 0) {
        return;
      }

      // Handle arrays - ensure they're preserved as arrays in URL
      if (Array.isArray(value) && value.length > 0) {
        // Make sure all array values are strings for consistency
        queryParams[key] = value.map(item => item.toString());
      } else if ((key === 'durationFrom' || key === 'durationTo') && (value === null || value === 'null')) {
        return;
      } else {
        // Add regular value to query params
        queryParams[key] = value;
      }
    });

    const url = this.router.createUrlTree([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge',
    }).toString();

    this.location.replaceState(url);

    this.getAll().subscribe();
  }

  getLikes(slug: string) {
    return this.http.get('/client/library/likes', {params: {slug}})
  }

  getFavourites(all = false, page = 1) {
    return this.http.get('/client/library/favourites', {params: {all, page}})
  }

  getQuoteFavourites() {
    return this.http.get('/client/library/quote/favourites')
  }

  getQuoteFavouritesContent() {
    return this.http.get('/client/library/quote/favourites/content')
  }

  getSimilar(slug: string) {
    return this.http.get('/client/library/similar', {params: {slug}})
  }

  getChapter(translationId: number, index: number) {
    return this.http.get('/client/library/chapter', {params: {translationId, index}})
  }
}
