import { BehaviorSubject, timer } from 'rxjs';
import { Injectable } from '@angular/core';
import { IToast } from "@/interfaces/toast";

@Injectable({
  providedIn: 'root',
})
export class ToasterService {
  private toastSubject = new BehaviorSubject<IToast[]>([]);
  public toasts$ = this.toastSubject.asObservable();
  private toastIdCounter = 0;

  showToast(message: string, color: string = 'default', position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'bottom-middle' = 'bottom-middle', duration: number = 3000) {
    const newToast: IToast = {
      id: `toast-${++this.toastIdCounter}`,
      message,
      color: color || 'default',
      position: position || 'bottom-middle',
      timer$: timer(duration)
    };

    const currentToasts = this.toastSubject.getValue();
    this.toastSubject.next([...currentToasts, newToast]);

    if (duration > 0) {
      newToast.timer$!.subscribe(() => {
        this.removeToast(newToast);
      });
    }
  }

  removeToast(toastToRemove: IToast) {
    const currentToasts = this.toastSubject.getValue();
    this.toastSubject.next(currentToasts.filter(toast => toast.id !== toastToRemove.id));
  }
}
