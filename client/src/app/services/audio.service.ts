import {inject, Injectable, signal} from '@angular/core';
import {ProfileService} from "./profile.service";
import {HttpClient} from "@angular/common/http";
import {Router} from "@angular/router";
import {catchError, EMPTY, of, tap, Subject} from "rxjs";
import { CookieService } from 'ngx-cookie-service';
import { ShareDataService } from './share-data.service';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class AudioService {
  cookieService = inject(CookieService);
  profileService = inject(ProfileService)
  shareDataService = inject(ShareDataService)
  authService = inject(AuthService)
  http = inject(HttpClient)
  router = inject(Router)
  data: any = signal(null)
  total: number = 0
  totalPages: number = 1
  tags: any = []
  filters: any = {
    page: 1
  }
  listened: any = []

  // Subject to emit filter results for toast notifications
  private filterResultsSubject = new Subject<{total: number, isFilterOperation: boolean}>();
  filterResults$ = this.filterResultsSubject.asObservable();

  // Flag to track if the current request is a filter operation
  private isFilterOperation = false;

  applyFilters(filters: any, isFilterOperation: boolean = true) {
    // Set flag to indicate this is a filter operation
    this.isFilterOperation = isFilterOperation;

    // Update internal filters first
    for(let key in filters) {
      if(filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
        this.filters[key] = filters[key];
      } else {
        delete this.filters[key]; // Remove empty values
      }
    }

    // Create a clean query params object
    const queryParams: any = {};

    // Only include non-empty values in the query parameters
    Object.keys(this.filters).forEach(key => {
      const value = this.filters[key];

      // Skip null, undefined, empty strings, and "null" string
      if (value === null || value === undefined || value === '' || value === 'null') {
        return;
      }

      // Skip empty arrays
      if (Array.isArray(value) && value.length === 0) {
        return;
      }

      // Handle arrays - ensure they're preserved as arrays in URL
      if (Array.isArray(value) && value.length > 0) {
        // Make sure all array values are strings for consistency
        queryParams[key] = value.map(item => item.toString());
      } else if ((key === 'durationFrom' || key === 'durationTo') && (value === null || value === 'null')) {
        return;
      } else {
        // Add regular value to query params
        queryParams[key] = value;
      }
    });

    // Navigate with clean params
    this.router.navigate([], {
      queryParams,
      replaceUrl: true // Replace the URL instead of adding to history
    });
  }


  changeQueryParams() {
    const queryParams = {...this.filters};
    delete queryParams.format;
    delete queryParams.youtube;
    delete queryParams.tags;
    this.router.navigate([], {queryParams})
  }

  get(external_id: string, view = true) {
    return this.http.get('/client/audio/' + external_id, {params: {view}})
  }

  getFilered(filters: any) {
    return this.http.get('/client/audio', {params: filters})
  }

  getAll() {
    if(this.authService.isAuth && !this.listened.length) {
      this.getListened().subscribe(res => this.listened = res)
    }
    // Create clean params object for the HTTP request
    const cleanParams: any = {};

    // Only include valid values
    Object.keys(this.filters).forEach(key => {
      const value = this.filters[key];

      // Skip empty, null, or 'null' string values
      if (value === null || value === undefined || value === '' || value === 'null') {
        return;
      }

      // Skip empty arrays
      if (Array.isArray(value) && value.length === 0) {
        return;
      }

      // Add to params
      cleanParams[key] = value;
    });

    // Use clean params for the HTTP request
    return this.http.get('/client/audio', { params: cleanParams }).pipe(
      tap((res: any) => {
        this.total = res.pagination.total;
        this.totalPages = res.pagination.totalPages;
        res.items = res.items.map((i: any) => {
          return {
            ...i,
            listened: this.listened.some((e: any) => e.audio && e.audio.id == i.id)
          };
        });
        this.data.set(res);

        // Emit filter results if this was a filter operation
        if (this.isFilterOperation) {
          this.filterResultsSubject.next({
            total: res.pagination.total,
            isFilterOperation: true
          });
          // Reset the flag
          this.isFilterOperation = false;
        }
      })
    );
  }


  like(id: number) {
    return this.http.post('/client/audio/like', {id}).pipe(
      // tap(() => this.getAll().subscribe()),
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    );
  }

  addToFavourites(id: number) {
    return this.http.post('/client/audio/favourite', {id}).pipe(
      tap(() => this.getAll().subscribe()),
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    );
  }

  savePosition(body: any) {
    if(!body.user_id) return this.savePositionCookie(body)
    return this.http.post('/client/audio/time/save', body).pipe()
  }

  savePositionCookie(body: any) {
    const audioPosition: any = JSON.parse(this.cookieService.get('audioPosition') || '{}')
    audioPosition[body.audio_id] = body.time
    this.cookieService.set('audioPosition', JSON.stringify(audioPosition))
    return of(audioPosition)
  }

  getSavedPositionCookie(body: any) {
    const audioPosition: any = JSON.parse(this.cookieService.get('audioPosition') || '{}')
    return of(audioPosition[body.audio_id])
  }

  getSavedPosition(body: any) {
    if(!body.user_id) return this.getSavedPositionCookie(body)
    return this.http.post('/client/audio/time', body).pipe()
  }

  download(url: string) {
    return this.http.get('/client/audio/download', {responseType: 'blob', params: {url}})
  }

  setAsListened(trackId: number) {
    return this.http.post('/client/audio/listened', {trackId})
  }

  getTags() {
    return this.http.get('/client/audio/tags').pipe(
      tap((res: any) => this.tags = res),
    )
  }

  getListened() {
    return this.http.get('/client/audio/listened')
  }

  getSimilar(key: string) {
    return this.http.get('/client/audio/similar', {params: {key}})
  }
}
