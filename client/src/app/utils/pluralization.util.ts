/**
 * Utility function for Russian pluralization
 * @param count - The number to determine pluralization for
 * @param forms - Array of three forms: [singular, few, many] (e.g., ['лекция', 'лекции', 'лекций'])
 * @returns The correct pluralized form
 */
export function pluralizeRussian(count: number, forms: [string, string, string]): string {
  const [singular, few, many] = forms;
  
  // Handle special cases for numbers ending in 11-14
  if (count % 100 >= 11 && count % 100 <= 14) {
    return many;
  }
  
  const lastDigit = count % 10;
  
  if (lastDigit === 1) {
    return singular;
  } else if (lastDigit >= 2 && lastDigit <= 4) {
    return few;
  } else {
    return many;
  }
}

/**
 * Formats the lecture count message with proper Russian pluralization
 * @param count - Number of lectures found
 * @returns Formatted message string
 */
export function formatLectureCountMessage(count: number): string {
  const pluralizedForm = pluralizeRussian(count, ['лекция', 'лекции', 'лекций']);
  return `Найдено ${count} ${pluralizedForm}`;
}
