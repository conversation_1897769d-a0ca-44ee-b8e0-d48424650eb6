import { pluralizeRussian, formatLectureCountMessage } from './pluralization.util';

describe('Pluralization Utility', () => {
  describe('pluralizeRussian', () => {
    const forms: [string, string, string] = ['лекция', 'лекции', 'лекций'];

    it('should return singular form for 1', () => {
      expect(pluralizeRussian(1, forms)).toBe('лекция');
    });

    it('should return singular form for numbers ending in 1 (except 11)', () => {
      expect(pluralizeRussian(21, forms)).toBe('лекция');
      expect(pluralizeRussian(31, forms)).toBe('лекция');
      expect(pluralizeRussian(101, forms)).toBe('лекция');
    });

    it('should return few form for 2-4', () => {
      expect(pluralizeRussian(2, forms)).toBe('лекции');
      expect(pluralizeRussian(3, forms)).toBe('лекции');
      expect(pluralizeRussian(4, forms)).toBe('лекции');
    });

    it('should return few form for numbers ending in 2-4 (except 12-14)', () => {
      expect(pluralizeRussian(22, forms)).toBe('лекции');
      expect(pluralizeRussian(23, forms)).toBe('лекции');
      expect(pluralizeRussian(24, forms)).toBe('лекции');
      expect(pluralizeRussian(102, forms)).toBe('лекции');
    });

    it('should return many form for 0, 5-20', () => {
      expect(pluralizeRussian(0, forms)).toBe('лекций');
      expect(pluralizeRussian(5, forms)).toBe('лекций');
      expect(pluralizeRussian(10, forms)).toBe('лекций');
      expect(pluralizeRussian(20, forms)).toBe('лекций');
    });

    it('should return many form for numbers ending in 11-14', () => {
      expect(pluralizeRussian(11, forms)).toBe('лекций');
      expect(pluralizeRussian(12, forms)).toBe('лекций');
      expect(pluralizeRussian(13, forms)).toBe('лекций');
      expect(pluralizeRussian(14, forms)).toBe('лекций');
      expect(pluralizeRussian(111, forms)).toBe('лекций');
      expect(pluralizeRussian(112, forms)).toBe('лекций');
    });

    it('should return many form for numbers ending in 0, 5-9', () => {
      expect(pluralizeRussian(25, forms)).toBe('лекций');
      expect(pluralizeRussian(30, forms)).toBe('лекций');
      expect(pluralizeRussian(100, forms)).toBe('лекций');
    });
  });

  describe('formatLectureCountMessage', () => {
    it('should format message correctly for different counts', () => {
      expect(formatLectureCountMessage(1)).toBe('Найдено 1 лекция');
      expect(formatLectureCountMessage(2)).toBe('Найдено 2 лекции');
      expect(formatLectureCountMessage(5)).toBe('Найдено 5 лекций');
      expect(formatLectureCountMessage(11)).toBe('Найдено 11 лекций');
      expect(formatLectureCountMessage(21)).toBe('Найдено 21 лекция');
      expect(formatLectureCountMessage(22)).toBe('Найдено 22 лекции');
      expect(formatLectureCountMessage(25)).toBe('Найдено 25 лекций');
    });
  });
});
