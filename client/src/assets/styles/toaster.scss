$success-color: rgba(58, 194, 121, 1);
$success-bg-color: rgba(240, 255, 247, 1);
$error-color: rgba(194, 30, 17, 1);
$error-bg-color: rgba(255, 239, 238, 1);
$warning-color: rgba(222, 165, 61, 1);
$warning-bg-color: rgba(255, 248, 236, 1);

.toast-container {
    display: flex;
    position: relative;
    width: fit-content;
    min-width: 350px;
    min-height: 82px;
    z-index: 3000;
    box-shadow: 0px 4px 16px 0px rgba(83, 46, 0, 0.25);
    border-radius: 10px;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 1);
    margin-bottom: 12px;
    zoom: 0.8;

    .toast-type-indicator {
        width: 9px;
        min-width: 9px;
        height: inherit;
        background-color: #333;
        position: sticky;
        z-index: 2;
    }

    .toast-body {
        display: flex;
        align-items: center;
        flex: 1 0 0;
        padding: 16px 33px;
        gap: 24px;
        position: relative;

        .toast-bg-img {
            position: absolute;
            width: 491px;
            height: 82px;
            left: 0;
            bottom: 0;
        }

        .toast-message {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 0 20px 0 0;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 20px;
            letter-spacing: 0;
            color: rgba(74, 74, 74, 1);
            border-right: 0.094rem solid #333;
            height: 100%;
            position: sticky;
            z-index: 2;
            flex: 1 0 0;

            .text {
                overflow-wrap: break-word;
                white-space: normal;
                max-width: calc(100vw - 125px);
            }
        }

        .close-toast-button {
            transition: opacity 200ms ease;
            position: sticky;
            z-index: 3;
            min-width: 16px;

            &:hover {
                opacity: 0.7;
            }
        }
    }

    &.success {
        background-color: $success-bg-color;

        .toast-type-indicator {
            background-color: $success-color;
        }

        .toast-body {
            .toast-bg-img {
                background: url(assets/images/success-toast-bg.svg);
            }

            .toast-message {
                border-color: $success-color;

                .toast-type-icon {
                    min-width: 30px;
                    height: 30px;
                    background: url(assets/images/icons/success-toast-icon.svg);
                }
            }

            .close-toast-button {
                svg {
                    path {
                        stroke: $success-color;
                    }
                }
            }
        }
    }

    &.error {
        background-color: $error-bg-color;

        .toast-type-indicator {
            background-color: $error-color;
        }

        .toast-body {
            .toast-bg-img {
                background: url(assets/images/error-toast-bg.svg);
            }

            .toast-message {
                border-color: $error-color;

                .toast-type-icon {
                    min-width: 30px;
                    height: 30px;
                    background: url(assets/images/icons/error-toast-icon.svg);
                }
            }

            .close-toast-button {
                svg {
                    path {
                        stroke: $error-color;
                    }
                }
            }
        }
    }

    &.warning {
        background-color: $warning-bg-color;

        .toast-type-indicator {
            background-color: $warning-color;
        }

        .toast-body {
            .toast-bg-img {
                background: url(assets/images/warning-toast-bg.svg);
            }

            .toast-message {
                border-color: $warning-color;

                .toast-type-icon {
                    min-width: 30px;
                    height: 30px;
                    background: url(assets/images/icons/warning-toast-icon.svg);
                }
            }

            .close-toast-button {
                svg {
                    path {
                        stroke: $warning-color;
                    }
                }
            }
        }
    }
}

app-toaster {
    position: fixed;
    display: flex;
    flex-direction: column-reverse;
    z-index: 3000;
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;

    // Improve animation performance
    will-change: opacity;
    backface-visibility: hidden;
}

app-toaster:has(.top-right) {
    top: 20px;
    right: 20px;
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
}

app-toaster:has(.top-left) {
    top: 20px;
    left: 20px;
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
}

app-toaster:has(.bottom-right) {
    bottom: 20px;
    right: 20px;
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
}

app-toaster:has(.bottom-left) {
    bottom: 20px;
    left: 20px;
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
}

app-toaster:has(.bottom-middle) {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
}

@media (max-width: 768px) {
    .toast-container {
        .toast-body {
            padding: 12px 24px;
            gap: 18px;

            .toast-message {
                font-size: 18px;
            }
        }
    }
}

@media (max-width: 500px) {
    .toast-container {
        .toast-body {
            .toast-message {
                font-size: 16px;
            }
        }
    }
}