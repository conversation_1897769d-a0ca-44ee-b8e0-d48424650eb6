 <div class="constructor-item">
  <dialog #modal>
    <div>
      {{message}}
    </div>
    <div class="text-center mt-6">
      <button (click)="closeModal(modal)" class="cancel-button">Да</button>
    </div>
  </dialog>
    <div *ngIf="item" class="flex justify-between items-center">
      <div class="flex items-center">
        <div class="constructor-item__date">{{item.date}}</div>
        <div class="constructor-item__name">{{item.text}}</div>
      </div>
      <div class="flex gap-3">
        <button class="btn btn-primary btn-sm" (click)="showEditForm()">Редактировать</button>
        <button class="btn btn-danger btn-sm" (click)="delete(item.id)">Удалить</button>
      </div>
    </div>
   <dialog #editForm>
     <form [formGroup]="form" class="space-y-3" style="min-width: 600px">
       <div>
         <div class="flex items-center">
           <input formControlName="show" type="checkbox" class="form-checkbox" checked>
           Показывать
           <span title="Показывать" class="cursor-pointer mx-2">
          &#9432;
        </span>
         </div>
       </div>
       <div>
         <label>Ссылка</label>
         <div [ngClass]="{ 'has-error': form.get('link')?.invalid && form.get('link')?.touched }" class="flex items-center">
           <input required formControlName="link" type="text" class="form-input"><span class="asterix">*</span>
           <span title="Ссылка" class="cursor-pointer mx-2">
          &#9432;
        </span>
         </div>
       </div>
       <div>
         <label>Дата</label>
         <div [ngClass]="{ 'has-error': form.get('date')?.invalid && form.get('date')?.touched }" class="flex items-center">
           <input required formControlName="date" type="date" class="form-input"><span class="asterix">*</span>
           <span title="Дата" class="cursor-pointer mx-2">
          &#9432;
        </span>
         </div>
       </div>
       <div [ngClass]="{ 'has-error': form.get('text')?.invalid && form.get('text')?.touched }">
         <label>Текст</label>
         <div  class="flex items-center">
           <textarea required formControlName="text" class="form-textarea"></textarea><span class="asterix">*</span>
           <span title="Текст" class="cursor-pointer mx-2">
          &#9432;
        </span>
         </div>
       </div>
       <div class="flex flex-col">
         <label>Изображение</label>
         <div [ngClass]="{ 'has-error': form.get('file')?.invalid && form.get('file')?.touched }" class="flex items-center">
           <input required type="file" class="form-input" (change)="uploadFile($event, 'image', false)"><span class="asterix">*</span>
           <span title="Изображение" class="cursor-pointer mx-2">
          &#9432;
        </span>
         </div>
         @if(form.value.image) {
           <PhotoPreview [disableRemoveBtn]="true" [items]="[form.value.image]"/>
         }
       </div>
       <div class="flex gap-2 items-center">
         <button [disabled]="form.invalid" type="submit" class="btn btn-primary btn-sm" (click)="onSubmit()">Сохранить</button>
         <button class="btn btn-sm" (click)="closeEditForm()">Закрыть</button>
       </div>
     </form>
   </dialog>
  </div>


