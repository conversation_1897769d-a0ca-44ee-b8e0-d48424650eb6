import {Component, inject} from '@angular/core';
import { NgScrollbar } from 'ngx-scrollbar';
import { IconModule } from "@/shared/icon/icon.module";
import {RouterLink, Router} from "@angular/router";
import {CommonModule} from "@angular/common";
import {GroupsEnum, UserService} from "@/services/user.service";

@Component({
    selector: 'sidebar',
    imports: [NgScrollbar, IconModule, RouterLink, CommonModule],
    templateUrl: './sidebar.component.html',
    styleUrl: './sidebar.component.scss'
})
export class SidebarComponent {
  router = inject(Router);
  userService = inject(UserService);
  activeDropdown: string[] = [];
  public GroupsEnum = GroupsEnum;

  isActiveRoute(route: string): boolean {
    return this.router.url.includes(route);
  }

  toggleAccordion(name: string, parent?: string) {
    if (this.activeDropdown.includes(name)) {
      this.activeDropdown = this.activeDropdown.filter((d) => d !== name);
    } else {
      this.activeDropdown.push(name);
    }
  }
}
