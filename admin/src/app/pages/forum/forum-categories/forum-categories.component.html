<button class="btn btn-primary" routerLink="/forum/categories/add">Создать категорию</button>
<dialog #modal>
  <div>
    {{message}}
  </div>
  <div class="text-center mt-6">
    <button (click)="closeModal(modal)" class="cancel-button">Да</button>
  </div>
</dialog>
<dialog #confirmDialog>
  <div>
    {{ message }}
  </div>
  <div class="mt-6 text-center">
    <button class="ok-button">Да</button>
    <button class="cancel-button ml-4">Отмена</button>
  </div>
</dialog>
<div class="panel mt-5">
  <table>
    <thead>
        <tr>
          <th>Название</th>
          <th>Описание</th>
          <th>Сортировка</th>
          <th></th>
        </tr>
    </thead>
    <tbody>
        @for(category of categories; track category.id) {
          <tr>
            <td>{{category.name}}</td>
            <td>{{category.description}}</td>
            <td>{{category.sort}}</td>
            <td>
              <div class="flex gap-2">
                <button class="btn btn-sm" (click)="router.navigate(['/forum/categories/' + category.id])">Редактировать</button>
                <button class="btn btn-sm btn-danger" (click)="deleteCategory(category.id)">Удалить</button>
              </div>
            </td>
          </tr>
        }
    </tbody>
  </table>
</div>
