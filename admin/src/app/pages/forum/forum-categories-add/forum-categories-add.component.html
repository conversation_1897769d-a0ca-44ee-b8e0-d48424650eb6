<div class="panel">
  <dialog #modal>
    <div>
      {{message}}
    </div>
    <div class="text-center mt-6">
      <button (click)="closeModal(modal)" class="cancel-button">Да</button>
    </div>
  </dialog>
  <form [formGroup]="form" class="space-y-3">
    <div>
      <label>Название</label>
      <input type="text" class="form-input" formControlName="name">
    </div>
    <div>
      <label>Описание</label>
      <textarea formControlName="description" class="form-textarea" rows="5"></textarea>
    </div>
    <div>
      <label>Сортировка</label>
      <input formControlName="sort" type="number" class="form-input">
    </div>
    <div>
      <label>Иконка</label>
      <input required type="file" accept="image/*" class="form-input" (change)="uploadIcon($event)">
      @if(form.value.icon) {
        <PhotoPreview [items]="[form.value.icon]"/>
      }
    </div>
    <div>
      <label>Недоступен для статусов</label>
      <label *ngFor="let status of statuses; let i = index">
        <input class="ckbx"
          type="checkbox"
          [checked]="isStatusUnavailable(status.value)"
          (change)="onStatusChange($event, status.value)"
        />
        {{ status.label }}
      </label>
    </div>
    <div>
      <button class="btn btn-success" [disabled]="isSubmitting || form.invalid" (click)="onSubmit()">
        Сохранить
        <svg *ngIf="isSubmitting" class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </button>
    </div>
  </form>
</div>
