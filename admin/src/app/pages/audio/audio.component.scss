.btn-import {
  position: relative;
}
.btn-import input {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}

@import "primeicons/primeicons.css";
:host ::ng-deep .p-datatable {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
  :host ::ng-deep .p-datatable {
    .p-datatable-thead > tr > th,
    .p-datatable-tbody > tr > td {
      max-width: 200px; // Set your desired max width
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  :host ::ng-deep .p-datatable .p-datatable-header {
    background: white;
    border: none;
    padding: 1rem;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    color: #374151;
    padding: 1rem;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr {
    background: white;
    border-bottom: 1px solid #e5e7eb;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
    background: #f9fafb;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    border: none;
  }
  
  :host ::ng-deep .p-button.p-button-text {
    color: #6b7280;
  }
  
  :host ::ng-deep .p-datatable .p-sortable-column:hover {
    background: #f9fafb;
  }

  :host ::ng-deep .p-datatable {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
  
  :host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    color: #000000;
    padding: 1rem;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    border: none;
    color: #000000;
  }
  
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
    background: #f9fafb;
  }
  
  :host ::ng-deep .p-sortable-column-icon {
    color: #000000;
  }
  
  :host ::ng-deep .p-rowgroup-footer td {
    font-weight: 700;
}

:host ::ng-deep .p-rowgroup-header {
    span {
        font-weight: 700;
    }

    .p-row-toggler {
        vertical-align: middle;
        margin-right: .25rem;
    }
}

.audio-player-modal {
  padding: 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.modal-body {
  display: flex;
  justify-content: center;
}

.cursor-pointer {
  cursor: pointer;
}

audio {
  width: 100%;
}
