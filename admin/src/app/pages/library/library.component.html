<div class="flex gap-3">
  <!-- <button class="btn btn-primary btn-import">
    Импорт
    <input type="file" (change)="import($event)">
  </button> -->
</div>
<dialog #modal>
  <div>
    {{message}}
  </div>
  <div class="text-center mt-6">
    <button (click)="closeModal(modal)" class="cancel-button">Да</button>
  </div>
</dialog>
<dialog #confirmModal>
  <div>
    {{message}}
  </div>
  <div class="mt-6 text-center">
    <button (click)="confirmDelete(true, confirmModal)" class="ok-button">Да</button>
    <button (click)="confirmDelete(false, confirmModal)" class="cancel-button ml-4">Отмена</button>
  </div>
</dialog>
<div class="panel mt-10">

  <p-table
  [columns]="selectedColumns"
  [value]="libraryService.list()"
  dataKey="id"
  [scrollable]="true"
  [resizableColumns]="true"
  [reorderableColumns]="true"
  columnResizeMode="expand"
  styleClass="p-datatable-gridlines"
  scrollHeight="800px"
  [tableStyle]="{'min-width': '10rem'}">
<ng-template #header let-columns>
  <tr>
    @for(col of columns; track col.id) {
      <th pResizableColumn pReorderableColumn [pSortableColumn]="col.field">
        {{col.header}} <p-sortIcon [field]="col.field" />
      </th>
    }

  </tr>
</ng-template>
<ng-template #body let-book let-columns="columns">
  <tr >
    @for(col of columns; track col.id) {
      <td>
        @switch (col.field) {
          @case ('ru') {
            {{getTitle('ru', book.translations)}}
          }
          @case ('en') {
            {{getTitle('en', book.translations)}}
          }
          @case ('de') {
            {{getTitle('de', book.translations)}}
          }
          @case ('ua') {
            {{getTitle('ua', book.translations)}}
          }
          @case ('it') {
            {{getTitle('it', book.translations)}}
          }
          @default {
            {{book.translations[1][col.field]}}
          }
        }
      </td>
    }
  </tr>
</ng-template>
  <ng-template #emptymessage>
      <tr>
          <td colspan="6">Нет данных</td>
      </tr>
  </ng-template>
</p-table>

</div>
