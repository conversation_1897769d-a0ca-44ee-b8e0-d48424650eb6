<div class="panel">
  <button class="p-2 ml-auto block" (click)="goBack()">X</button>
  <div class="mb-5">
    <div class="mt-3 flex flex-wrap border-b border-white-light dark:border-[#191e3a]">
      @for(language of languages; track $index) {
        <a
          href="javascript:"
          class="-mb-[1px] block border border-transparent p-3.5 py-2 !outline-none transition duration-300 hover:text-primary dark:hover:border-b-black"
          [ngClass]="{ '!border-white-light !border-b-white  text-primary dark:!border-[#191e3a] dark:!border-b-black': selectedLanguage === language }"
          (click)="selectLanguage(language)"
        >
          {{language.toUpperCase()}}
        </a>
      }
    </div>
  </div>
  <dialog #modal>
    <div>
      {{message}}
    </div>
    <div class="text-center mt-6">
      <button (click)="closeModal(modal)" class="cancel-button">Да</button>
    </div>
  </dialog>
    @for(item of forms; track item.lang) {
      @if(selectedLanguage == item.lang) {
        <form [formGroup]="item.form" action="" class="space-y-3">
          <div>
            <label>Название галереи</label>
            <div [ngClass]="{ 'has-error': item.form.get('title')?.invalid && item.form.get('title')?.touched }" class="flex items-center">
              <input required formControlName="title" type="text" class="form-input"><span class="asterix">*</span>
              <span title="Название галереи" class="cursor-pointer mx-2">
                &#9432;
              </span>
            </div>
          </div>
          <div>
            <label>SEO-заголовок</label>
            <div [ngClass]="{ 'has-error': item.form.get('seo_title')?.invalid && item.form.get('seo_title')?.touched }" class="flex items-center">
              <input required formControlName="seo_title" type="text" class="form-input"><span class="asterix">*</span>
              <span title="SEO-заголовок" class="cursor-pointer mx-2">
                &#9432;
              </span>
            </div>
          </div>
          <div>
            <label>SEO-описание</label>
            <div [ngClass]="{ 'has-error': item.form.get('seo_description')?.invalid && item.form.get('seo_description')?.touched }" class="flex items-center">
              <input required formControlName="seo_description" type="text" class="form-input"><span class="asterix">*</span>
              <span title="SEO-описание" class="cursor-pointer mx-2">
                &#9432;
              </span>
            </div>
          </div>
          <div>
            <label>Обложка</label>
            <div [ngClass]="{ 'has-error': item.form.get('file')?.invalid && item.form.get('file')?.touched }" class="flex items-center relative">
              <input required type="file" accept="image/*" class="form-input" (change)="uploadFile($event, 'cover', false)" [disabled]="isUploadingCover">
              <span class="asterix">*</span>
              <span title="Обложка" class="cursor-pointer mx-2">&#9432;</span>

              <div *ngIf="isUploadingCover" class="absolute right-12">
                <svg class="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            </div>
            @if(files && files[item.lang] && files[item.lang]['cover'] && files[item.lang]['cover']['name']) {
              <PhotoPreview [disableRemoveBtn]="true" (onItemRemoved)="removeFile($event)" [items]="[files[item.lang]['cover']]"/>
            }
            <!-- @if(id && item.form.value?.cover) {
              <PhotoPreview [items]="[item.form.value['cover']]"/>
            } -->
          </div>
          <div>
            <label>Фотографии</label>
            <div [ngClass]="{ 'has-error': item.form.get('file')?.invalid && item.form.get('file')?.touched }" class="flex items-center relative">
              <input required type="file" multiple accept="image/*" class="form-input" (change)="uploadFile($event, 'photos')" [disabled]="isUploadingPhotos">
              <span class="asterix">*</span>
              <span title="Фотографии" class="cursor-pointer mx-2">&#9432;</span>

              <!-- Add loading indicator -->
              <div *ngIf="isUploadingPhotos" class="absolute right-12">
                <svg class="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            </div>
            @if(files && files[item.lang] && files[item.lang]['photos']) {
              <PhotoPreview [items]="files[item.lang]['photos']"/>
            }
            <!-- @if(id && item.form.value['photos']) {
              <PhotoPreview [items]="item.form.value['photos']"/>
            } -->
          </div>
          <div>
            <button [disabled]="item.form.invalid || isUploadingPhotos || isUploadingCover || isSubmitting ||
            !files[item.lang]['photos'].length ||
            !files[item.lang]['cover']"
                    class="btn btn-success flex items-center gap-2"
                    (click)="onSubmit()">
                <span>Сохранить</span>
                <svg *ngIf="isSubmitting" class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </button>
          </div>
        </form>
      }
    }

</div>
