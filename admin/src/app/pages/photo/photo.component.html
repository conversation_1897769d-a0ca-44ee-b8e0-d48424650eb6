<button [routerLink]="'/photo/add'" class="btn btn-primary">Добавить фотогалерею</button>
<dialog #modal>
  <div>
    {{message}}
  </div>
  <div class="text-center mt-6">
    <button (click)="closeModal(modal)" class="cancel-button">Да</button>
  </div>
</dialog>
<dialog #confirmDialog>
  <div>
    {{ message }}
  </div>
  <div class="mt-6 text-center">
    <button class="ok-button">Да</button>
    <button class="cancel-button ml-4">Отмена</button>
  </div>
</dialog>
<div class="panel mt-5">
  <div class="table-responsive">
    <table>
      <thead>
        <tr>
          <th>Название папки</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
      @for(item of photoService.photos; track $index) {
        <tr>
          <td>{{item.translations[0].title}}</td>
          <td>
            <div class="flex gap-2">
              <button class="btn btn-sm" (click)="open(item)">Перейти</button>
              <button class="btn btn-primary btn-sm" (click)="router.navigate(['/photo/' + item.id])">Редактировать</button>
              <button class="btn btn-danger btn-sm" (click)="delete(item.id)">Удалить</button>
            </div>
          </td>
        </tr>
      }
      </tbody>
    </table>
  </div>
</div>
