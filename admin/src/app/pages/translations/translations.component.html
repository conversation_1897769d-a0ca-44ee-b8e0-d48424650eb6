<div class="flex gap-3">
  <button type="button" class="btn btn-primary" [routerLink]="['/translations/add']">Добавить элемент</button>
</div>

<div class="panel mt-10">
  <dialog #dialog>
    <div>
      {{message}}
    </div>
    <div class="mt-6 text-center">
      <button (click)="closeModal(dialog)" class="cancel-button">OK</button>
    </div>
  </dialog>
  <div class="table-responsive">
    <table>
      <thead>
      <tr>
        <th>Код</th>
        <th>RU</th>
        <th>EN</th>
        <th>DE</th>
        <th>UA</th>
        <th>IT</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
        @if(translationService.translations()) {
          @for(translation of translationService.translations(); track translation.code) {
            <tr>
              <td>{{translation.code}}</td>
              @for(lang of translation.translations; track lang) {
                <td>{{lang.text}}</td>
              }
              <td>
                <div class="flex gap-3">
                  <button class="btn btn-primary btn-sm" (click)="router.navigate(['/translations/' + translation.id])">Редактировать</button>
                  <button class="btn btn-danger btn-sm" (click)="delete(translation.code)">Удалить</button>
                </div>
              </td>
            </tr>
          }
        }
      </tbody>
    </table>
  </div>
</div>

