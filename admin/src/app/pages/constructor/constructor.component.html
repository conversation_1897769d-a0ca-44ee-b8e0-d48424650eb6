<button (click)="carousel.showCarouselForm(true)" class="btn btn-primary">Создать карусель</button>
<dialog #modal>
  <div>
    {{message}}
  </div>
  <div class="text-center mt-6">
    <button (click)="closeModal(modal)" class="cancel-button">Да</button>
  </div>
</dialog>
<dialog #confirmDialog>
  <div>
    {{ message }}
  </div>
  <div class="mt-6 text-center">
    <button class="ok-button">Да</button>
    <button class="cancel-button ml-4">Отмена</button>
  </div>
</dialog>
<div class="panel table-responsive mt-5">
  <table>
    <thead>
      <tr>
        <th>ID</th>
        <th>Название</th>
        <th>Тип</th>
        <th>Размер</th>
        <th>Элементов</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      @for(item of carousels.value; track item.id) {
        <tr>
          <td>{{item.id}}</td>
          <td>{{item.title}}</td>
          <td>{{item.type}}</td>
          <td>{{item.size}}</td>
          <td>{{item.items.length}}</td>
          <td>
            <div class="flex gap-3">
              <button (click)="edit($index, item)" class="btn btn-primary btn-sm">Редактировать</button>
              <button (click)="delete(item.id)" class="btn btn-danger btn-sm">Удалить</button>
            </div>
          </td>
        </tr>
      }
    </tbody>
  </table>
  <carousel (onItemAdded)="onCarouselAdded($event)" (onItemEdited)="onCarouselEdited($event)"/>
</div>
