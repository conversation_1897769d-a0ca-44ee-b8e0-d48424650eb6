<div class="panel table-responsive">
  <dialog #modal>
    <div>
      {{message}}
    </div>
    <div class="text-center mt-6">
      <button (click)="closeModal(modal)" class="cancel-button">Да</button>
    </div>
  </dialog>
  <dialog #confirmDialog>
    <div>
      {{ message }}
    </div>
    <div class="mt-6 text-center">
      <button class="ok-button">Да</button>
      <button class="cancel-button ml-4">Отмена</button>
    </div>
  </dialog>

  <div class="filters flex flex-wrap gap-3 mb-5">
    <div class="w-auto">
      <label class="block mb-1">Поиск по ФИО, email</label>
      <input type="text" class="form-input" [(ngModel)]="searchTerm">
    </div>
    
    <div class="w-auto">
      <label class="block mb-1">Группа</label>
      <select class="form-select" [(ngModel)]="groupQuery">
        <option value="">Не выбрано</option>
        <option value="ADMIN">Администраторы сайта</option>
        <option value="FORUM_ADMIN">Администраторы форума</option>
        <option value="FORUM_MODERATOR">Модераторы форума</option>
        <option value="VISITOR">Посетители</option>
        <option value="STUDENT">Ученики</option>
        <option value="PERSONAL_SITE">Личные сайты</option>
        <option value="STUDENT_LIST">Работа со списком учеников</option>
        <option value="NEW">Новые анкеты</option>
      </select>
    </div>
    
    <div class="w-auto">
      <label class="block mb-1">Статус</label>
      <select class="form-select" [(ngModel)]="statusQuery">
        <option value="">Не выбрано</option>
        <option value="NEW">Новичок</option>
        <option value="ZSVD">Заочный символ Веры Санатана Дхармы</option>
        <option value="OSVD">Очный символ Веры Санатана Дхармы</option>
        <option value="ZSVP">Заочный символ Веры Прибежища</option>
        <option value="OSVP">Очный символ Веры Прибежища</option>
      </select>
    </div>
    
    <div class="w-auto">
      <label class="block mb-1">Подтвержден</label>
      <select class="form-select" [(ngModel)]="confirmedQuery">
        <option value="">Не выбрано</option>
        <option value="true">Да</option>
        <option value="false">Нет</option>
      </select>
    </div>
    
    <div class="w-auto">
      <label class="block mb-1">Активность</label>
      <select class="form-select" [(ngModel)]="activeQuery">
        <option value="">Не выбрано</option>
        <option value="true">Да</option>
        <option value="false">Нет</option>
      </select>
    </div>

    <div class="w-auto">
      <label class="block mb-1">Страна</label>
      <p-select [options]="countries" [(ngModel)]="countryQuery" optionValue="iso_code2" optionLabel="name_ru" [filter]="true" filterBy="name_ru" [showClear]="true" placeholder="Select a Country" class="w-full md:w-56">
        <ng-template #selectedItem let-selectedOption>
            <div class="flex items-center gap-2">
                <img [src]="selectedOption.flag_url" style="width: 18px" />
                <div>{{ selectedOption.name_ru }}</div>
            </div>
        </ng-template>
        <ng-template let-country #item>
            <div class="flex items-center gap-2">
                <img [src]="country.flag_url"  style="width: 18px" />
                <div>{{ country.name_ru }}</div>
            </div>
        </ng-template>
    </p-select>
    </div>
  </div>

  


  <p-table
      [columns]="selectedColumns"
      [value]="filteredUsers"
      dataKey="id"
      [scrollable]="true"
      [resizableColumns]="true"
      [reorderableColumns]="true"
      columnResizeMode="expand"
      styleClass="p-datatable-gridlines"
      scrollHeight="800px"
      [tableStyle]="{'min-width': '10rem'}">
   <ng-template #caption>
    <p-multiselect
        display="chip"
        [options]="cols"
        [(ngModel)]="selectedColumns"
        optionLabel="header"
        selectedItemsLabel="{0} columns selected"
        [style]="{'min-width': '200px'}"
        placeholder="Choose Columns" 
        (onChange)="onColumnSelectionChange()"/>
    </ng-template>
    <ng-template #header let-columns>
      <tr>
          <th *ngFor="let col of columns" pResizableColumn pReorderableColumn [pSortableColumn]="col.field">
              {{col.header}} <p-sortIcon [field]="col.field" />
          </th>

          <th style="width: 5rem"></th>
          <th style="width: 5rem"></th>
      </tr>
    </ng-template>
    <ng-template #body let-user let-columns="columns">
      <tr (click)="router.navigate(['/users/' + user.id])">
        <td *ngFor="let col of columns">
            @switch (col.field) {
              @case ('groups') {
                {{groupLabel(user.groups)}}
              }
              @case ('createdAt') {
                {{formatDate(user.createdAt)}}
              }
              @case ('lastActivity') {
                {{formatDate(user.lastActivity)}}
              }
              @case ('statuses') {
                {{statusLabel(user.statuses)}}
              }
              @case ('active') {
                {{user.active ? 'Да' : 'Нет'}}
              }
              @case ('confirmed') {
                {{user.confirmed ? 'Да' : 'Нет'}}
              }
              @case ('country') {
                {{findCountryByIsoCode(user.country)?.name_ru}}
              }
              @case ('avatar') {
                <img class="profile-avatar" style="width: 50px; height: 50px;" *ngIf="user.avatar" [src]="environment.serverUrl + '/upload/'  + user.avatar!.name" alt="">
              }
              @default {
                {{user[col.field]}}
              }
            }
        </td>
        <td>
          <i (click)="router.navigate(['/users/' + user.id])" class="pi pi-file-edit"></i>
        </td>
        <td>
          <i (click)="$event.stopPropagation();deleteUser(user.id)"  class="pi pi-trash"></i>
        </td>
    </tr>
  </ng-template>
      <ng-template #emptymessage>
          <tr>
              <td colspan="6">Нет данных</td>
          </tr>
      </ng-template>
  </p-table>

</div>

<dialog #editDialog style="min-width: 420px">
  <form [formGroup]="editForm" class="space-y-3">
    <div class="flex">
      <input type="checkbox" class="form-checkbox" formControlName="active">
      <label>Активность</label>
    </div>
    <div class="flex">
      <input type="checkbox" class="form-checkbox" formControlName="confirmed">
      <label>Подтвержден</label>
    </div>
    <div>
      <label>Имя</label>
      <input type="text" formControlName="firstName" class="form-input">
    </div>
    <div>
      <label>Отчество</label>
      <input type="text" formControlName="middleName" class="form-input">
    </div>
    <div>
      <label>Фамилия</label>
      <input type="text" formControlName="lastName" class="form-input">
    </div>
    <div>
      <label>Духовное имя</label>
      <input type="text" formControlName="spiritualName" class="form-input">
    </div>
    <div>
      <label>E-mail</label>
      <input type="text" formControlName="email" [disabled]="true" class="form-input">
    </div>
    <div>
      <label>Группа</label>
      <select class="form-select" formControlName="groups" multiple>
        <option *ngFor="let group of groups" [value]="group.value">{{group.label}}</option>
      </select>
    </div>
    <div>
      <label>Статус</label>
      <select class="form-select" formControlName="statuses" multiple>
        <option *ngFor="let status of statuses" [value]="status.value">{{status.label}}</option>
      </select>
    </div>
    <div class="flex gap-2">
      <button class="btn btn-sm btn-success" (click)="saveUser()">Сохранить</button>
      <button class="btn btn-sm" (click)="closeDialog()">Закрыть</button>
    </div>
  </form>
</dialog>
