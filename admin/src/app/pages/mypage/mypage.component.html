<button class="btn btn-primary" routerLink="/mypage/add">Создать страницу</button>
<dialog #modal>
  <div>
    {{message}}
  </div>
  <div class="text-center mt-6">
    <button (click)="closeModal(modal)" class="cancel-button">Да</button>
  </div>
</dialog>
<dialog #confirmDialog>
  <div>
    {{ message }}
  </div>
  <div class="mt-6 text-center">
    <button class="ok-button">Да</button>
    <button class="cancel-button ml-4">Отмена</button>
  </div>
</dialog>
<div class="panel mt-5">
  <table>
    <thead>
      <tr>
        <th>Название</th>
        <th>Описание</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      @for(item of items; track item.id) {
        <tr>
          <td>{{item.title}}</td>
          <td>{{item.description}}</td>
          <td>
            <div class="flex gap-2">
              <button class="btn btn-sm btn-primary" (click)="router.navigate(['/mypage/' + item.id])">Редактировать</button>
              <button class="btn btn-sm btn-danger" (click)="removePage(item.id)">Удалить</button>
            </div>
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>
