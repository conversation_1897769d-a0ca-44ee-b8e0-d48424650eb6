<button class="btn btn-primary" (click)="showDialog()">Добавить элемент</button>

<div class="panel mt-5">
  <table>
    <thead>
      <tr>
        <th>Изображение</th>
        <th>Заголовок</th>
        <th>Описание</th>
        <th>Тип</th>
        <th>Ссылка</th>
        <th>Частота</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      @for(item of items; track item.id) {
        <tr>
          <td>
            @if (item.image) {
              <img style="width: 80px; height: 80px; object-fit: cover" [src]="environment.serverUrl + '/upload/' + item.image.name" alt="">
            }
          </td>
          <td>{{item.title}}</td>
          <td>{{item.description}}</td>
          <td>
            @switch(item.type) {
              @case("1") {
                Модалка
              }

              @case("2") {
                Баннер
              }
            }
          </td>
          <td>{{item.link}}</td>
          <td>{{item.freq}}</td>
          <td>
            <div class="flex gap-2">
              <button class="btn btn-primary btn-sm" (click)="edit(item)">Редактировать</button>
              <button class="btn btn-danger btn-sm" (click)="remove(item.id)">Удалить</button>
            </div>
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>


<dialog #dialog style="min-width: 420px">
  <form [formGroup]="form" class="space-y-3">
    <div>
      <label><input formControlName="active" type="checkbox" style="margin-right: 10px" class="form-checkbox">Календарь</label>
    </div>
    <div>
      <label><input formControlName="advertising" type="checkbox" style="margin-right: 10px" class="form-checkbox">Реклама</label>
    </div>
    <div>
      <label>Заголовок</label>
      <input type="text" class="form-input" formControlName="title">
    </div>
    <div>
      <label>Тип</label>
      <select formControlName="type" class="form-select">
        <option value="1">Модалка</option>
        <option value="2">Баннер</option>
      </select>
    </div>
    <div>
      <label>Описание</label>
      <textarea formControlName="description" class="form-textarea"></textarea>
    </div>
    <div>
      <label>Дата</label>
      <input type="date" formControlName="date" class="form-input" />
    </div>
    <div>
      <label>Изображение</label>
      <input type="file" class="form-input"  accept="image/*" (change)="uploadFile($event)">
      @if(form.value.image) {
        <PhotoPreview [disableRemoveBtn]="true" [items]="[form.value.image]"/>
      }
    </div>
    <div>
      <label>Ссылка</label>
      <input type="text" class="form-input" formControlName="link">
    </div>
    <div>
      <label>Количество показов {{form.value.freq}}</label>
      <input type="number" class="form-input" formControlName="freq">
    </div>
    <div>
      <button [disabled]="form.invalid" class="btn btn-primary" (click)="onSubmit()">Сохранить</button>
      <button class="btn btn-primary" (click)="this.closeDialog()">Закрыть</button>
    </div>
  </form>
</dialog>
