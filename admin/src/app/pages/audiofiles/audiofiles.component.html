<div class="flex">
  <!-- <button class="btn btn-primary btn-import">
    Импорт
    <input type="file" (change)="import($event)">
  </button> -->
</div>
<dialog #modal>
  <div>
    {{message}}
  </div>
  <div class="text-center mt-6">
    <button (click)="closeModal(modal)" class="cancel-button">Да</button>
  </div>
</dialog>

<!-- Audio Player Modal -->
<dialog #audioPlayerModal class="audio-player-modal">
  <div class="modal-header">
    <h3>{{selectedAudio?.title}}</h3>
    <button class="close-button" (click)="closeAudioModal(audioPlayerModal)">×</button>
  </div>
  <div class="modal-body">
    <audio controls *ngIf="selectedAudio">
      <source [src]="selectedAudio.url">
      Your browser does not support the audio element.
    </audio>
  </div>
</dialog>

<div class="panel mt-5">
  <div class="field-sort mt-1 mb-5">
    <form [formGroup]="filters" class="space-y-3">
      <div>
        <label>Название</label>
        <div class="flex items-center">
          <input formControlName="title" type="text" class="form-input" (input)="applyFilter()">
          <span title="Название" class="cursor-pointer mx-2">
            &#9432;
          </span>
        </div>
      </div>
      <div>
        <label>Тип</label>
        <div class="flex items-center">
          <select class="form-select" formControlName="types" (change)="applyFilter()">
            @for(type of types; track type.id) {
              <option [value]="type.id">{{type.name}}</option>
            }
          </select>
        </div>
      </div>

      <div>
        <label>Исполнитель</label>
        <div class="flex items-center">
          <select class="form-select" formControlName="singers" (change)="applyFilter()">
            @for(type of singers; track type.id) {
              <option [value]="type.id">{{type.name}}</option>
            }
          </select>
        </div>
      </div>

      <div>
        <label>Тег</label>
        <div class="flex items-center">
          <select class="form-select" formControlName="tags" (change)="applyFilter()">
            @for(type of tags; track type.id) {
              <option [value]="type.id">{{type.name}}</option>
            }
          </select>
        </div>
      </div>
    </form>
  </div>

  <p-table
    [columns]="selectedColumns"
    [value]="items"
    dataKey="id"
    [scrollable]="true"
    [resizableColumns]="true"
    [reorderableColumns]="true"
    columnResizeMode="expand"
    styleClass="p-datatable-gridlines"
    scrollHeight="800px"
    [tableStyle]="{'min-width': '10rem'}"
    [paginator]="true"
    [rows]="50"
    [rowsPerPageOptions]="[50, 100, 200]">
    <ng-template #caption>
      <p-multiselect
        display="chip"
        [options]="columns"
        [(ngModel)]="selectedColumns"
        optionLabel="header"
        selectedItemsLabel="{0} columns selected"
        [style]="{'min-width': '200px'}"
        placeholder="Choose Columns" />
    </ng-template>
    <ng-template #header let-columns>
      <tr>
        <th *ngFor="let col of columns" pResizableColumn pReorderableColumn [pSortableColumn]="col.field">
          {{col.header}} <p-sortIcon [field]="col.field" />
        </th>

        <th style="width: 5rem"></th>
      </tr>
    </ng-template>
    <ng-template #body let-audio let-columns="columns">
      <tr >
        <td *ngFor="let col of columns"
            (click)="openAudioPlayer(audio, audioPlayerModal)" class="cursor-pointer">
          @switch (col.field) {
            <!-- @case ('link') {
              <audio controls>
                <source src="{{audio.link}}">
              </audio>
            } -->
            @case ('likes') {
              {{audio.likes}}
            }
            @case ('favourites') {
              {{favouritesCount(audio.id)}}
            }
            @case('tags') {
              {{getTagsName(audio[col.field])}}
            }
            @default {
              {{audio[col.field]?.name || audio[col.field]}}
            }
          }
        </td>

      </tr>
    </ng-template>
    <ng-template #emptymessage>
      <tr>
        <td colspan="6">Нет данных</td>
      </tr>
    </ng-template>
  </p-table>

</div>
