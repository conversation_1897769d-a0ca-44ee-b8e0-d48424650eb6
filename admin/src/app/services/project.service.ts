import {inject, Injectable, signal} from '@angular/core';
import {HttpClient, HttpErrorResponse} from "@angular/common/http";
import {catchError, tap, throwError} from "rxjs";



@Injectable({
  providedIn: 'root'
})
export class ProjectService {
  http = inject(HttpClient);
  list = signal<{id: number, translations: {id: number, title: string, activeFrom: Date, activeTo: Date, preview: string}[]}[]>([])

  get(id: number) {
    return this.http.get('/admin/project/' + id)
  }

  getAll() {
    return this.http.get('/admin/project').pipe(
      tap((res: any) => this.list.set(res))
    )
  }

  create(form: any) {
    return this.http.post('/admin/project', form).pipe(
      catchError((error: HttpErrorResponse)  => {
        alert(JSON.stringify(error.error));
        return throwError(() => new Error(error.error));
      })
    )
  }

  update(id: number, form: any) {
    return this.http.patch(`/admin/project/${id}`, form)
  }

  delete(id: number) {
    return this.http.delete(`/admin/project/${id}`).pipe(
      tap(() => this.getAll().subscribe())
    )
  }

}
