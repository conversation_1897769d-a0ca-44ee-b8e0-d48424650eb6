import {inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {tap} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class AudioService {
  private http = inject(HttpClient);
  total: number = 0;
  list: {title: string, author: string, date: Date, link: string, status: string, views: number, likes: any, favourites: any}[] = []
  getAll(filters: any) {
    return this.http.get('/admin/audio', {params: filters}).pipe(
      tap((res: any) => {
        this.list = res.items
        this.total = res.total
      }),
    )
  }
  getAuthors() {
    return this.http.get('/admin/audio/authors')
  }
  importFromFile(body: FormData) {
    return this.http.post('/admin/audio/import', body)
  }

  getTags() {
    return this.http.get('/admin/audio/tags')
  }

  getAudioFiles() {
    return this.http.get('/firebase/audiofiles')
  }
}
