import {inject, Injectable, signal} from '@angular/core';
import {HttpClient, HttpErrorResponse} from "@angular/common/http";
import {catchError, tap, throwError} from "rxjs";



@Injectable({
  providedIn: 'root'
})
export class EventService {
  http = inject(HttpClient);
  list = signal<{id: number, translations: {id: number, title: string, activeFrom: Date, activeTo: Date, preview: string}[]}[]>([])

  get(id: number) {
    return this.http.get('/admin/event/' + id)
  }

  getAll() {
    return this.http.get('/admin/event').pipe(
      tap((res: any) => this.list.set(res))
    )
  }

  create(form: any) {
    return this.http.post('/admin/event', form).pipe(
      catchError((error: HttpErrorResponse)  => {
        alert(JSON.stringify(error.error));
        return throwError(() => new Error(error.error));
      })
    )
  }

  update(id: number, form: any) {
    return this.http.patch(`/admin/event/${id}`, form)
  }

  delete(id: number) {
    return this.http.delete(`/admin/event/${id}`).pipe(
      tap(() => this.getAll().subscribe())
    )
  }

}
