import {inject, Injectable, signal} from '@angular/core';
import {HttpClient, HttpErrorResponse} from "@angular/common/http";
import {catchError, tap, throwError} from "rxjs";



@Injectable({
  providedIn: 'root'
})
export class LibraryService {
  http = inject(HttpClient);
  list = signal<{id: number, translations: {id: number, title: string, author: string, reader: string}[]}[]>([])

  get(id: number) {
    return this.http.get('/admin/library/' + id)
  }

  getAll() {
    return this.http.get('/admin/library').pipe(
      tap((res: any) => this.list.set(res))
    )
  }

  create(form: any) {
    return this.http.post('/admin/library', form).pipe(
      catchError((error: HttpErrorResponse)  => {
        alert(JSON.stringify(error.error));
        return throwError(() => new Error(error.error));
      })
    )
  }

  update(id: number, form: any) {
    return this.http.patch(`/admin/library/${id}`, form)
  }

  delete(id: number) {
    return this.http.delete(`/admin/library/${id}`).pipe(
      tap(() => this.getAll().subscribe())
    )
  }

  importFromFile(body: FormData) {
    return this.http.post('/admin/library/import', body)
  }

}
