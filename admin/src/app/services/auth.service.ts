import {Injectable, inject} from "@angular/core";
import { HttpClient } from '@angular/common/http';
import {map, tap} from "rxjs";
import {CookieService} from 'ngx-cookie-service';
import { Router } from '@angular/router'

interface IAuth {
    username?: string | null | undefined,
    password?: string | null | undefined
}

interface IAuthResponse {
    accessToken: string
}

@Injectable({
    providedIn: 'root'
})
export class AuthService {
    router = inject(Router)
    http = inject(HttpClient)
    cookieService = inject(CookieService)
    token: string | null = this.cookieService.get('token')
    get isAuth() {
        return !!this.token
    }
    login(form: IAuth) {
        return this.http.post<IAuthResponse>('/user/signin', form).pipe(
            tap(response => {
              console.log(response)
                this.token = response.accessToken
                this.cookieService.set('token', this.token, {path: '/'})
            })
        )
    }

    logout() {
      this.token = null
      this.cookieService.delete('token')
      this.router.navigate(['/login'])
    }
}
