import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { UserService } from '@/services/user.service';
import { of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export const authGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const cookieService = inject(CookieService);
  const userService = inject(UserService);

  const token = cookieService.get('token');

  if (!token) {
    router.navigate(['/login']);
    return false;
  }

  if (userService.profile) {
    return of(true);
  }

  return userService.getProfile().pipe(
    map(profile => {
      if (profile) return true;
      router.navigate(['/login']);
      return false;
    }),
    catchError(() => {
      router.navigate(['/login']);
      return of(false);
    })
  );
};
